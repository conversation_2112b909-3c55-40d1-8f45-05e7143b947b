{"bannerMenu": "<PERSON>ks<PERSON> <strong><PERSON></strong> kini me<PERSON> <strong><PERSON><PERSON></strong>. Kelola data dan hak akses karyawan lebih mudah disini.", "title": "<PERSON><PERSON><PERSON>", "careerHistory": "Riway<PERSON> Ka<PERSON>r", "or": "atau", "filter": {"selectedEmployee_one": "{{count}} <PERSON><PERSON><PERSON>", "selectedEmployee_other": "{{count}} <PERSON><PERSON><PERSON>", "sendAccess": "<PERSON><PERSON> ma<PERSON>"}, "table": {"columnTitle": {"employeeName": "NAMA KARYAWAN", "id": "NIP", "defaultOutlet": "DEFAULT OUTLET", "totalOutlet": "JUMLAH OUTLET", "jobPosition": "POSISI", "permissionName": "HAK AKSES", "activeStatus": "STATUS KARYAWAN"}, "subRowLabel": {"bankInfo": "Informasi Bank", "accessDate": "Tanggal Kirim <PERSON>", "lastAccessed": "<PERSON><PERSON><PERSON>"}, "button": {"view": "<PERSON><PERSON>", "edit": "<PERSON><PERSON>", "move": "<PERSON><PERSON><PERSON><PERSON>", "viewAccess": "Lihat Hak Akses", "terminate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "delete": "Hapus"}, "accessStatusLabel": {"notSent": "Belum Terkirim", "sent": "Terkirim"}}, "form": {"title": {"add": "<PERSON><PERSON> Karyawan", "edit": "<PERSON><PERSON>", "view": "<PERSON><PERSON>", "employeeRotation": "<PERSON><PERSON><PERSON><PERSON>", "terminateEmployee": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "stepLabel": {"primaryData": "Data Utama", "personalData": "Data Personal", "basicInformation": "Informasi <PERSON>", "employeeData": "<PERSON>", "employeeDetail": "<PERSON><PERSON>", "organizationStructure": "Struktur Organisasi", "contact": "Kontak", "workingExperience": "Pengalaman <PERSON>", "studyExperience": "Pendidikan", "personalID": "Identitas Pribadi", "personalDocs": "Dokumen <PERSON>", "HRLetter": "Surat HR", "emergencyContact": "Kontak Darurat", "liability": "Tanggungan", "salaryPayment": "<PERSON><PERSON><PERSON><PERSON>", "organizationalRotation": "Perpindahan Organisasi", "additionalFile": "<PERSON>", "reason": "<PERSON><PERSON><PERSON>", "previousDirectReportInformation": "Informasi Atasan Sebelumnya", "settingTeamMember": "Pengaturan Anggota Team", "editDirectReport": "Ubah Atasan", "job": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "stepInfo": {"personalData": "<strong>Data Personal</strong> dapat dilengkapi secara bertahap", "employeeData": "<strong><PERSON></strong> dapat dileng<PERSON>pi secara bertahap"}, "content": {"primaryData": {"label": {"photo": "Foto <PERSON>", "image": "Gambar", "name": "<PERSON><PERSON>", "id": "Nomor Induk <PERSON> (NIP)", "employeeAccess": "<PERSON><PERSON><PERSON>", "phone": "Telepon", "department": "Departemen", "position": "<PERSON><PERSON><PERSON>", "access": "Hak Akses", "changePIN": "Ubah PIN"}, "caption": {"employeeAccess": {"active": "<PERSON><PERSON><PERSON>wan Aktif", "inactive": "<PERSON><PERSON><PERSON>wan Tidak Aktif"}, "defaultPIN": "Default: 123456 (<PERSON><PERSON><PERSON><PERSON> 6 digit)"}, "tooltips": {"employeeAccess": "<PERSON><PERSON><PERSON><PERSON> akses karyawan akan mempeng<PERSON>hi akses ke Dashboard dan majoo App"}, "placeholder": {"name": "Contoh: <PERSON><PERSON>", "phone": "Contoh: 0811111111111", "email": "Contoh: <EMAIL>", "position": "<PERSON>toh: Manager"}}, "employeeDetail": {"label": {"departement": "Departemen", "position": "<PERSON><PERSON><PERSON>", "grade": "Ting<PERSON>", "joinDate": "<PERSON><PERSON>", "joinLocation": "Lokasi <PERSON>", "employeeType": "<PERSON><PERSON><PERSON>", "firstDate": "<PERSON><PERSON>", "managerName": "Nama Manager", "effectiveDate": "Tanggal Efektif", "endDate": "<PERSON><PERSON>", "keepData": "Data Tetap", "previousDirectReport": "Atasan Sebelumnya", "teamMemberName": "Nama Anggota Team", "newDirectReport": "<PERSON><PERSON><PERSON>", "dataNoChange": "Tidak Ada Perubahan", "directReport": "<PERSON><PERSON>"}}, "personalData": {"label": {"id": "NIK (Nomor Induk Kependudukan)", "uploadIdCard": "KTP", "birthPlace": "Tempat Lahir", "birthDate": "<PERSON><PERSON>", "maritalStatus": "Status Perkawinan", "city": "Kota/Kabupaten", "postalCode": "Kode Pos", "gender": "<PERSON><PERSON>", "status": "Status", "religion": "<PERSON><PERSON>a", "kk": "<PERSON><PERSON><PERSON>"}, "placeholder": {"id": "Contoh: 11 11 11 ***********", "birthPlace": "Contoh: <PERSON><PERSON><PERSON>", "birthDate": "<PERSON><PERSON><PERSON>", "address": "Contoh: Jln. Merdeka No 1", "postalCode": "Contoh: 55584"}}, "employeeData": {"label": {"joinDate": "<PERSON><PERSON>", "employeeStatus": "Status Karyawan", "endWorkPeriod": "<PERSON><PERSON><PERSON>", "employmentBpjsNo": "BPJS Ketenagakerjaan", "employmentBpjsPhoto": "Foto Kartu BPJS Ketenagakerjaan", "healthBpjsNo": "BPJS Kesehatan", "healthBpjsPhoto": "Foto Kartu BPJS Kesehatan", "bankName": "Nama Bank", "accountNo": "Nomor <PERSON>", "accountOwner": "<PERSON><PERSON> Pem<PERSON>k <PERSON>", "accountBookPhoto": "Foto Buku Rekening", "accountVerification": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "accountBook": "B<PERSON>", "bpjsPhoto": "BPJS Kesehatan", "employmentBpjs": "BPJS Ketenagakerjaan"}, "caption": {"bankIntegrated": "Berhasil! Rekening Bank Raya telah terhubung", "employeeStatus": {"active": "Karyawan aktif be<PERSON>", "inactive": "Karyawan tidak aktif bekerja di <PERSON>"}}, "placeholder": {"employeeStatus": "Pilih <PERSON> Karyawan", "employmentBpjsNo": "Contoh: *************", "healthBpjsNo": "Contoh: *************", "accountNo": "Contoh: 7632-01-007520-53-0", "accountOwner": "Contoh: <PERSON><PERSON>"}, "tooltips": {"employeeStatus": "Pengaturan status karyawan akan mempeng<PERSON>hi akses karyawan ke majoo TEAMS"}}, "contact": {"label": {"personalEmail": "<PERSON><PERSON>", "phone": "<PERSON><PERSON>", "samePhone": "<PERSON><PERSON>n dengan no telepon di step awal", "addressByIDNumber": "<PERSON><PERSON><PERSON>", "addressByResidential": "<PERSON><PERSON><PERSON>", "provinceByResidential": "<PERSON><PERSON><PERSON>", "cityByResidential": "Kota/Kab <PERSON>"}}, "workingExperience": {"label": {"companyName": "<PERSON><PERSON><PERSON><PERSON>", "companyNamePlaceholder": "Contoh: PT. Jaya", "position": "<PERSON><PERSON><PERSON>", "startDate": "<PERSON><PERSON>", "endDate": "<PERSON><PERSON>"}}, "studyExperience": {"label": {"educationalDegree": "<PERSON><PERSON><PERSON>", "educationDepartment": "<PERSON><PERSON><PERSON>", "educationDepartmentPlaceholder": "Contoh: <PERSON><PERSON><PERSON><PERSON> dan <PERSON>", "institution": "Lembaga", "institutionPlaceholder": "Contoh: Universitas Indonesia", "gpa": "IPK", "gpaPlaceholder": "Contoh: 4.0"}}, "personalID": {"label": {"npwp": "Nomor NPWP", "bpjstk": "Nomor BPJS Ketenagakerjaan", "bpjs": "Nomor BPJS Kesehatan"}}, "personalDocs": {"label": {"clearanceLetter": "<PERSON><PERSON><PERSON>", "diploma": "<PERSON><PERSON><PERSON><PERSON>"}}, "HRLetter": {"label": {"workingDescription": "<PERSON><PERSON><PERSON><PERSON>", "letterOfAssignment": "SK Saat Ini"}}, "relationshipDetail": {"label": {"name": "<PERSON><PERSON>", "relationship": "Hubungan", "relationshipPlaceholder": "Contoh: Suami/Isteri", "phone": "<PERSON><PERSON>"}}, "salaryPayment": {"label": {"bankName": "Nama Bank", "accountNumber": "Nomor <PERSON>", "bankAccountPhoto": "Foto Buku Rekening", "bankAccountOwner": "<PERSON><PERSON> Pem<PERSON>k <PERSON>"}}, "fileUploadDesc": "File upload JPEG, PNG, doc, docx, atau PDF dengan maksimum 5 mb", "pictureUploadDesc": "File upload JPEG atau PNG dengan maksimum 5 mb", "terminateEmployee": {"terminateOption": "<PERSON><PERSON>", "reason": "<PERSON><PERSON><PERSON>", "attachment": "Lam<PERSON>ran", "prevDirectReport": "Atasan Sebelumnya", "directReport": "<PERSON><PERSON><PERSON>"}}}, "modal": {"cancelModal": {"title": {"add": "<PERSON><PERSON>", "edit": "<PERSON><PERSON>"}, "description": {"add": "Membatalkan <strong><PERSON><PERSON></strong> akan menghapus seluruh data yang telah diinput dan tidak dapat dibatalkan. Lanjutkan?", "edit": "Membatalkan <strong><PERSON><PERSON></strong> akan menghapus seluruh data yang telah diinput dan tidak dapat dibatalkan. Lanjutkan?"}}, "deleteModal": {"title": "<PERSON><PERSON>", "description": "<PERSON>gh<PERSON><PERSON> karyawan dengan nama <strong>{{name}}</strong> akan menghilangkan data tersebut secara permanen dari daftar & tidak dapat dibatalkan. Lanjutkan?"}, "addModal": {"title": "<PERSON><PERSON><PERSON>", "description": "Data karyawan dengan nama <strong>{{name}}</strong> akan disimpan dan tampil di daftar karyawan.<br /><PERSON>lih <strong>Simpan</strong> untuk menyimpan data karyawan.<br /><PERSON>lih <strong>Simpan dan <PERSON><PERSON></strong> untuk menyimpan data karyawan serta memberi akses tautan <PERSON> Teams melalui WhatsApp."}, "editModal": {"title": "<PERSON><PERSON><PERSON>", "description": "Perubahan data karyawan dengan nama <strong>{{name}}</strong> akan disimpan dan tampil di daftar karyawan. Lanjutkan?"}, "verifyEmailModal": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Kode verifikasi sudah dikirimkan melalui email ke {{email}}", "label": {"verificationCode": "<PERSON><PERSON>", "notReceived": "Belum menerima kode?", "resend": "<PERSON><PERSON>", "waiting": "<PERSON><PERSON> tunggu {{counter}} detik untuk mengirim ulang"}}, "nikChangedModal": {"title": "KTP Diubah", "description": "NIK pada data karyawan dengan nama <strong>{{name}}</strong> terintegrasi dengan <PERSON>, per<PERSON><PERSON> akan menghapus integrasi dengan satu sehat. Apakah anda yakin?"}, "editPIN": {"title": "Ubah PIN", "label": {"new": "PIN Bar<PERSON>", "repeat": "Ulangi PIN Baru"}, "placeholder": "Contoh: 123456"}, "sendMajooTeamsAccessModal": {"title": "<PERSON><PERSON> ma<PERSON>", "description": {"invalid": "Terdapat karyawan dengan data yang belum lengkap. Silakan lengkapi data <strong>No. Telepon</strong> karyawan berikut sebelum melanjutkan: {{dataEmployee}}", "confirmed_one": "<PERSON><PERSON><PERSON> <strong>({{count}} orang)</strong> yang dipilih akan mendapatkan tautan untuk mengakses majoo Teams melalui Whatsapp. Lanjutkan?", "confirmed_other": "<PERSON><PERSON><PERSON> <strong>({{count}} orang)</strong> yang dipilih akan mendapatkan tautan untuk mengakses majoo Teams melalui Whatsapp. Lanjutkan?"}}, "changeActiveStatusModal": {"title": "Ubah Status Karyawan", "description": "Perubahan status karyawan <strong>{{name}}</strong> men<PERSON>di Tidak Aktif akan menghilangkan akses karyawan ke aplikasi karyawan dan P<PERSON> Majoo. Lanjutkan?"}, "moveEmployee": {"title": "<PERSON><PERSON><PERSON>", "title_member": "<PERSON><PERSON><PERSON><PERSON>", "title_report": "<PERSON><PERSON><PERSON>", "description": "Data karyawan yang telah diubah akan disimpan dan tampil pada daftar karyawan, apa<PERSON>h anda yakin?", "description_member": "<PERSON><PERSON><PERSON> yang akan dipindahkan memiliki Anggota Team, silakan lakukan pembaruan data Anggota Team", "description_report": "{{ total }} <PERSON><PERSON><PERSON> dipilih, silakan pilih atasan untuk {{ total }} karyawan tersebut", "button_member": "Atur Anggota Team"}, "carrerHistory": {"title": "<PERSON><PERSON><PERSON><PERSON> - {{ name }}", "subtitle": "Daftar Riwayat <PERSON>"}, "terminateEmployee": {"title": "<PERSON><PERSON><PERSON>", "description": "Data karyawan yang telah melakukan <PERSON>n  akan dinonaktifkan pada daftar karyawan, apa<PERSON>h anda yakin?", "descriptionHasMember": "<PERSON><PERSON><PERSON> yang akan diakhiri memiliki Anggota Team, silakan lakukan pembaruan data Anggota Team"}, "export": {"title": "Ekspor Daftar Karyawan", "description": "<PERSON><PERSON>, sistem sedang memproses Daftar <PERSON>. <PERSON>a akan menerima pemberitahuan notifikasi dan email saat data siap untuk diunduh.", "description_confirm": "<PERSON><PERSON><PERSON> akan diekspor dan disimpan di perangkat yang digunakan. Lanjutkan?", "description_process": "Daftar Karyawan sedang diproses. Daftar Karyawan dapat diunduh setelah selesai diproses."}, "import": {"title": "Impor <PERSON>", "title_document": "<PERSON><PERSON><PERSON>", "title_close": "Batal Impor Data", "title_export": "Ekspor Template", "description": "<PERSON><PERSON>, sistem sedang memproses {{ name }}. Anda akan menerima pemberitahuan notifikasi & email saat data selesai diproses", "description_close": "<PERSON><PERSON><PERSON> dari halaman ini mengakibatkan proses impor batal, anda harus mengulang proses dari awal.", "description_export": "Template Impor Daftar Karyawan di ekspor dan disimpan di perangkat yang digunakan. Lanjutkan?", "description_exportProcess": "<PERSON><PERSON>, sistem sedang memproses Template Impor <PERSON>. Anda akan menerima pemberitahuan pada halaman notifikasi dan email saat template siap untuk diunduh.", "helper": "<PERSON><PERSON>h tipe dokumen yang akan dilakukan impor data", "helper_employee": "Saat mengimpor data karyawan, pastikan format sesuai dengan file template yang disediakan.", "helper_template": "Tentukan template berkas yang akan digunakan", "field": {"docType": "<PERSON><PERSON><PERSON> Dokumen", "upload": "Unggah Data", "template": {"label": "Belum memiliki Template", "label_has": "Sudah memiliki Template", "helper": "Gunakan template yang telah disediakan di bawah ini", "helper_has": "Pastikan telah menggunakan template yang disediakan majoo"}}, "notices": ["<PERSON><PERSON><PERSON><PERSON> setiap kolom yang memiliki tanda bintang (*) karena bersifat wajib diisi", "<PERSON><PERSON><PERSON> anda telah membuat master data Departemen, <PERSON><PERSON><PERSON> dan <PERSON>", "Cek sheet “Instruksi” untuk membaca instruksi cara pengisian template impor", "Sistem akan menolak proses tambah data karyawan jika:<ul><li>Nomor Induk Pegawai (NIP) sudah digunakan</li><li>Terdapat salah satu data departemen, tingkat atau posisi yang tidak terdaftar</li><li>Terdapat kolom yang wajib diisi kosong</li><li>Terdapat inputan yang tidak sesuai dengan ketentuan</li></ul>", "Pastikan mengimpor file dalam format Microsoft Excel (xls atau xlsx)"], "notices_document": ["Harap menguggah file dalam bentuk .zip", "Dokumen di dalam file .zip diharapkan menggunakan format JPEG, PNG, DOC, DOCX, atau PDF.", "<PERSON><PERSON> setiap dokumen diharapkan menggunakan Nomor Induk Pegawai (NIP).Contoh: EMP12345.jpg", "Ukuran maksimum setiap dokumen adalah 5 MB."]}}, "display": {"label": {"addEmployee": "<PERSON><PERSON> Karyawan", "gender": {"male": "Pria", "female": "<PERSON><PERSON>"}, "maritalStatus": {"single": "<PERSON><PERSON>", "married": "<PERSON><PERSON>", "divorced": "<PERSON><PERSON>", "widowed": "<PERSON><PERSON>"}, "religion": {"protestant": "Protestan", "catholic": "Katolik"}, "employmentStatus": {"fullTime": "<PERSON><PERSON><PERSON>", "contract": "<PERSON><PERSON><PERSON>", "freelance": "<PERSON><PERSON><PERSON><PERSON>"}, "previous": "Sebelumnya", "saveAndGrantAccess": "<PERSON><PERSON><PERSON> dan <PERSON>", "verify": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "later": "<PERSON><PERSON>", "keepSend": "Tetap <PERSON>", "completeData": "Lengkapi Data"}, "toast": {"addSuccess": "<PERSON><PERSON><PERSON> be<PERSON><PERSON><PERSON>", "editSuccess": "<PERSON> karya<PERSON> ber<PERSON><PERSON>h", "editAdminRestriction": "Tidak diperkenankan melihat data Admin/Owner", "editManagerRestriction": "Tidak diperkenankan melihat data Manager", "deleteSuccess": "<PERSON><PERSON><PERSON> <strong>{{name}}</strong> ber<PERSON><PERSON> di<PERSON>pus", "deleteErrorTransaction": "<PERSON><PERSON><PERSON> terhubung dengan trans<PERSON>i ber<PERSON>lan", "userAccessIncomplete": "Data karyawan belum lengkap. Pastikan no HP telah diisi lengkap", "addEmployeError": {"name": "<PERSON>a sudah pernah digunakan", "phone": "Nomor HP sudah pernah digunakan di cabang ini", "email": "Email yang kamu gunakan telah terdaftar sebelumnya", "nip": "NIP sudah digunakan"}, "resendVerificationCodeError": "<PERSON>l mengirim ulang kode verifikasi", "verificationError": "<PERSON>de veri<PERSON><PERSON>i salah, silakan ulangi kembali", "changePINerror": "Gagal mengubah PIN karyawan", "changePINSuccess": "<PERSON><PERSON> <strong>{{name}}</strong> ber<PERSON><PERSON> di<PERSON>h", "verifiyEmail": {"resend": {"title": "Kode verifikasi telah di<PERSON>", "description": "<PERSON><PERSON><PERSON> cek email {{email}} untuk mendapatkan kode verifikasi anda"}, "success": "Verifikasi user dengan hak akses {{role}}"}, "sendMajooTeamsAccessSuccess": "<PERSON><PERSON><PERSON> <strong>{{employees}}</strong> ber<PERSON><PERSON> mendapatkan akses majoo <PERSON>", "moveSuccess": "<PERSON><PERSON><PERSON><PERSON>", "terminateSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "failedDeleteHasSubordinates": "Tidak dapat dihapus karena memiliki anggota team"}, "inputErrorMessage": {"email": "Email tidak boleh kosong", "nId": "NIK tidak boleh kosong", "nIdLength": "NIK (Nomor Induk Kependudukan) diisi dengan 16 digit", "name": "<PERSON>a tidak boleh kosong", "employeeId": "Nomor induk pegawai tidak boleh kosong", "access": "<PERSON><PERSON><PERSON> tidak boleh kosong", "outlet": "Outlet tidak boleh kosong", "pinLength": "PIN menggunakan 6 digit angka", "pinLength2": "*PIN harus menggunakan 6 digit angka", "pinUnmatch": "*PIN tidak sesuai", "emailVerification": "Dibutuhkan verifikasi email", "unverifiedEmail": "Unverified: <PERSON><PERSON>ri<PERSON> email terle<PERSON><PERSON> da<PERSON>u", "placeOfBirth": "Tempat lahir tidak boleh kosong", "dateOfBirth": "Tanggal lahir tidak boleh kosong", "gender": "<PERSON><PERSON> kelamin tidak boleh kosong", "religion": "Agama tidak boleh kosong", "maritalStatus": "Status perkawinan tidak boleh kosong", "phone": "Nomor Ponsel tidak boleh kosong", "alamatKTP": "Alamat KTP tidak boleh kosong", "npwp": "Nomor NPWP tidak boleh kosong", "bankName": "Nama Bank tidak boleh kosong", "bankAccount": "Nomor rekening tidak boleh kosong", "bankOwnerName": "<PERSON><PERSON> pemilik rekening tidak boleh kosong", "endDate": "Tanggal Berak<PERSON> tidak boleh kosong", "cantEmpty": " {{ field }} tidak boleh kosong"}}, "dataInputSelect": {"optionTerminate": {"resign": "Mengundurkan Di<PERSON>", "workTermination": "Pemutusan Hubungan Kerja"}}, "banner": {"import": {"title": "Impor Data Karyawan Sedang Diproses", "description": "Tombol \"Impor data\" akan dinonaktifkan sementara hingga proses Impor data karyawan <PERSON>"}}}
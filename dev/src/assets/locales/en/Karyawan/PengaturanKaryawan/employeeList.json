{"bannerMenu": "Access <strong>Employee Data</strong> now through <strong>Employee Menu</strong>. Manage employee data and access rights more easily here.", "title": "Employee List", "careerHistory": "Career History", "or": "or", "filter": {"selectedEmployee_one": "{{count}} Employee Selected", "selectedEmployee_other": "{{count}} Employees Selected", "sendAccess": "Send majoo Teams Access"}, "table": {"columnTitle": {"employeeName": "EMPLOYEE NAME", "id": "EIN", "defaultOutlet": "DEFAULT OUTLET", "totalOutlet": "NUMBER OF OUTLETS", "jobPosition": "POSITION", "permissionName": "ACCESS RIGHTS", "activeStatus": "EMPLOYEE STATUS"}, "subRowLabel": {"bankInfo": "Bank Information", "accessDate": "Access Date Sent", "lastAccessed": "Last Accessed"}, "button": {"view": "Detail Employee", "edit": "Edit Employee", "move": "Transfer Employee", "viewAccess": "View Access Rights", "terminate": "Termination", "delete": "Delete"}, "accessStatusLabel": {"notSent": "Not sent yet", "sent": "<PERSON><PERSON>"}}, "form": {"title": {"add": "Add Employee", "edit": "Edit Employee", "view": "Employee Detail", "employeeRotation": "Employee Rotation", "terminateEmployee": "Terminate Employee"}, "stepLabel": {"primaryData": "Primary Data", "personalData": "Personal Data", "basicInformation": "Basic Information", "employeeData": "Employee Data", "employeeDetail": "Employee Detail", "organizationStructure": "Organizational Structure", "contact": "Contact", "workingExperience": "Working Experience", "studyExperience": "Study Experience", "personalID": "Personal ID", "personalDocs": "Personal Documents", "HRLetter": "HR Letter", "emergencyContact": "Emergency Contact", "liability": "Liability", "salaryPayment": "Salary Payment", "organizationalRotation": "Organizational Rotation", "additionalFile": "Additional File", "reason": "Reason", "previousDirectReportInformation": "Previous Direct Report Information", "settingTeamMember": "Setting Team Member", "editDirectReport": "Edit Direct Report", "job": "Job"}, "stepInfo": {"personalData": "<strong>Personal Data</strong> can be completed gradually", "employeeData": "<strong>Employee Data</strong> can be completed gradually"}, "content": {"primaryData": {"label": {"photo": "Employee Photo", "image": "Image", "name": "Name", "id": "Employee ID (NIP)", "employeeAccess": "Employee Access", "phone": "Mobile", "department": "Department", "position": "Position", "access": "Access Rights", "changePIN": "Change PIN"}, "caption": {"employeeAccess": {"active": "Employee access is active", "inactive": "Employee access is inactive"}, "defaultPIN": "Default: 123456 (Maximum 6 digits)"}, "tooltips": {"employeeAccess": "Employee access settings will affect access to Dashboard and majoo App"}, "placeholder": {"name": "Example: <PERSON><PERSON>", "phone": "Example: 0811111111111", "email": "Example: <EMAIL>", "position": "Example: Manager"}}, "employeeDetail": {"label": {"departement": "Department", "position": "Position", "grade": "Grade", "joinDate": "Joining Date", "joinLocation": "Joining Location", "employeeType": "Employee Type", "firstDate": "Contract First Date", "managerName": "Manager Name", "effectiveDate": "Efective Date", "endDate": "End Date", "keepData": "Keep Data", "previousDirectReport": "Previous Direct Report", "teamMemberName": "Team Member Name", "newDirectReport": "New Direct Report", "dataNoChange": "Data not changed", "directReport": "Direct Report"}}, "personalData": {"label": {"id": "NIN (National Identity Number)", "uploadIdCard": "ID Card", "birthPlace": "Place of Birth", "birthDate": "Date of Birth", "maritalStatus": "Marital Status", "city": "City/District", "postalCode": "Postal Code", "gender": "Gender", "status": "Marital Status", "religion": "Religion", "kk": "<PERSON><PERSON><PERSON>"}, "placeholder": {"id": "Example: 11 11 11 ***********", "birthPlace": "Example: <PERSON><PERSON><PERSON>", "birthDate": "Select Date", "address": "Example: Jln. Merdeka No 1", "postalCode": "Example: 55584"}}, "employeeData": {"label": {"joinDate": "Joining Date", "employeeStatus": "Employee Status", "endWorkPeriod": "End of Employment Period", "employmentBpjsNo": "BPJS Employment", "employmentBpjsPhoto": "BPJS Employment Card Photo", "healthBpjsNo": "BPJS Health", "healthBpjsPhoto": "BPJS Health Card Photo", "bankName": "Bank Name", "accountNo": "Account Number", "accountOwner": "Account Holder", "accountBookPhoto": "Savings Book Photo", "accountVerification": "Account Verification", "accountBook": "Savings Book", "bpjsPhoto": "BPJS Health Card ", "employmentBpjs": "BPJS Employment"}, "caption": {"bankIntegrated": "Succeed! Bank Raya account has been connected", "employeeStatus": {"active": "Actively working in the company", "inactive": "Not actively working in the company"}}, "placeholder": {"employeeStatus": "Select Employee Status", "employmentBpjsNo": "Example: *************", "healthBpjsNo": "Example: *************", "accountNo": "Example: 7632-01-007520-53-0", "accountOwner": "Example: <PERSON><PERSON>"}, "tooltips": {"employeeStatus": "Employee status settings will affect employee access to majoo TEAMS"}}, "contact": {"label": {"personalEmail": "Personal Email", "phone": "Phone Number", "samePhone": "Copy the phone number as in the first step", "addressByIDNumber": "Address by ID Card", "addressByResidential": "Residential Address", "provinceByResidential": "Province by Residential", "cityByResidential": "City By Residential"}}, "workingExperience": {"label": {"companyName": "Previous Company Name", "companyNamePlaceholder": "Example: PT. Jaya", "position": "Position", "startDate": "Start Date", "endDate": "End Date"}}, "studyExperience": {"label": {"educationalDegree": "Educational Degree", "educationDepartment": "Educational Department", "educationDepartmentPlaceholder": "Example: Management dan <PERSON>", "institution": "Institution", "institutionPlaceholder": "Example: University of Indonesia", "gpa": "GPA", "gpaPlaceholder": "Example: 4.0"}}, "personalID": {"label": {"npwp": "NPWP Number", "bpjstk": "BPJS Employment Number", "bpjs": "BPJS Health Number"}}, "personalDocs": {"label": {"clearanceLetter": "Clearance Letter", "diploma": "Diploma"}}, "HRLetter": {"label": {"workingDescription": "Working", "letterOfAssignment": "Letter of Assignment"}}, "relationshipDetail": {"label": {"name": "Name", "relationship": "Relationship", "relationshipPlaceholder": "Example: Husband/Wife", "phone": "Phone Number"}}, "salaryPayment": {"label": {"bankName": "Bank Name", "accountNumber": "Account Number", "bankAccountPhoto": "Bank Account Photo", "bankAccountOwner": "Bank Account Owner Name"}}, "fileUploadDesc": "Upload a JPEG, PNG, doc, docx, or PDF file with a maximum size of 5 mb", "pictureUploadDesc": "Upload a JPEG or PNG file with a maximum size of 5 mb", "terminateEmployee": {"terminateOption": "Termination Options", "reason": "Reason", "attachment": "Attachment", "prevDirectReport": "Previous Direct Report", "directReport": "New Direct Report"}}}, "modal": {"cancelModal": {"title": {"add": "Cancel Add Employee", "edit": "Cancel Edit Employee"}, "description": {"add": "Canceling <strong>Add Employee</strong> will delete all data that has been inputted and cannot be undone. Continue?", "edit": "Canceling <strong>Edit Employee</strong> will delete all data that has been inputted and cannot be undone. Continue?"}}, "deleteModal": {"title": "Delete Employee", "description": "Deleting Employee for <strong>{{name}}</strong> will permanently remove the data and cannot be undone. Continue?"}, "addModal": {"title": "Save Employee Data", "description": "Employee data named <strong>{{name}}</strong> will be saved and appear in the employee list.<br />Select <strong>Save</strong> to save employee data.<br />Select <strong>Save and Grant Access</strong> to save employee data and provide access to the Employee App link via WhatsApp."}, "editModal": {"title": "Save Employee Data Changes", "description": "Changes to employee data named <strong>{{name}}</strong> will be saved and appear in the employee list. Continue?"}, "verifyEmailModal": {"title": "Email Verification", "description": "The verification code has been sent via email to {{email}}", "label": {"verificationCode": "Verification Code", "notReceived": "Didn't receive the code?", "resend": "Resend", "waiting": "Please wait {{counter}} seconds to resend"}}, "nikChangedModal": {"title": "Identity Card Changes", "description": "Identity Card on employee data with the name <PERSON><PERSON> is integrated with <PERSON><PERSON>, changing the identity card will remove the integration with <PERSON><PERSON>. Are you sure?"}, "editPIN": {"title": "Change PIN", "label": {"new": "New PIN", "repeat": "Repeat New PIN"}, "placeholder": "Example: 123456"}, "sendMajooTeamsAccessModal": {"title": "Send majoo Teams Access", "description": {"invalid": "There are employees with incomplete data. Please complete <strong>Phone Number</strong> data of the following employees before proceeding: {{dataEmployee}}", "confirmed_one": "Selected employees <strong>({{count}} person)</strong> will get a link to access majoo Teams via Whatsapp. Continue?", "confirmed_other": "Selected employees <strong>({{count}} persons)</strong> will get a link to access majoo Teams via Whatsapp. Continue?"}}, "changeActiveStatusModal": {"title": "Change Employee Status", "description": "Changes to employee status named <strong>{{name}}</strong> to Not Active will remove the employee's access to employee application and Majoo POS. Continue?"}, "moveEmployee": {"title": "Save Employee Rotation", "title_member": "Employee Rotation", "title_report": "Select Direct Report", "description": "The changed employee data will be saved and appear in the employee list, are you sure?", "description_member": "Employees who will be transferred have Team Members, please update the Team Member data.", "description_report": "{{ total }} Employee selected, please select direct report for {{ total }} employee that was selected", "button_member": "Update Team Members"}, "carrerHistory": {"title": "Career History - {{ name }}", "subtitle": "List of Employee Career History"}, "terminateEmployee": {"title": "Save Termination", "description": "Employee data that has terminated will be deactivated on the employee list, are you sure?", "descriptionHasMember": "Employees who will be terminated have Team Members, please update the Team Member data"}, "export": {"title": "Export Employee List", "description": "Please wait, the system is processing the Employee List. You will receive a notification and email when the data is ready to download.", "description_confirm": "All Employee Lists will be exported and saved to the device being used. Continue?", "description_process": "Employee List is being processed. Employee List can be downloaded after processing is complete."}, "import": {"title": "Import Employee Data", "title_document": "Import Employee Documents", "title_close": "Cancel Import Data", "title_export": "Export Template", "description": "Please wait, the system is processing {{ name }}. You will receive a notification and email when the data processing is complete", "description_close": "Exiting this page will cancel the Recipe import process, you will have to repeat the process from the beginning.", "description_export": "Import Employee List template will be exported and saved to the device being used. Continue?", "description_exportProcess": "Please wait, the system is processing the Import Employee List Template. You will receive a notification and email when the template is ready to download.", "helper": "Select the document type for data import", "helper_employee": "When importing employee data, make sure the format matches the provided template file.", "helper_template": "Define the file template to be used", "field": {"docType": "Document Type", "upload": "Upload Data", "template": {"label": "Don't have <PERSON><PERSON><PERSON>", "label_has": "Already have Template", "helper": "Use the template provided below", "helper_has": "Make sure to use the template provided by majoo"}}, "notices": ["Complete all columns with an asterisk (*) as they are mandatory", "Make sure you have created master data for Department, Level and Position", "Check the 'Instructions' sheet to read instructions on how to fill the import template", "The system will reject the employee data addition process if:<ul><li>Employee ID (NIP) is already in use</li><li>There is a department, level or position data that is not registered</li><li>There are empty mandatory fields</li><li>There are inputs that do not comply with requirements</li></ul>", "Make sure to import files in Microsoft Excel format (xls or xlsx)"], "notices_document": ["Please upload files in .zip format", "Documents inside the .zip file should use JPEG, PNG, DOC, DOCX, or PDF format.", "Each document name should use Employee ID (NIP). Example: EMP12345.jpg", "Maximum size for each document is 5 MB."]}}, "display": {"label": {"addEmployee": "Add Employee", "gender": {"male": "Male", "female": "Female"}, "maritalStatus": {"single": "Single", "married": "Married", "divorced": "Divorced", "widowed": "Widowed"}, "religion": {"protestant": "Protestant", "catholic": "Catholic"}, "employmentStatus": {"fullTime": "Full Time", "contract": "Contract", "freelance": "Freelance"}, "previous": "Previous", "saveAndGrantAccess": "Save and Grant Access", "verify": "Verify", "later": "Later", "keepSend": "Keep Send", "completeData": "Complete Data"}, "toast": {"addSuccess": "Employee successfully added", "editSuccess": "Employee data successfully updated", "editAdminRestriction": "Not allowed to see Admin/Owner data", "editManagerRestriction": "Not allowed to see Manager data", "deleteSuccess": "Employee <strong>{{name}}</strong> successfully deleted", "deleteErrorTransaction": "Emloyee connected with on going transaction", "userAccessIncomplete": "Incomplete employee data. Make sure phone number are filled.", "addEmployeError": {"name": "The name already used", "phone": "Phone number already used in this branch", "email": "The email has been previously registered", "nip": "NIP already used"}, "resendVerificationCodeError": "Failed to resend verification code", "verificationError": "Incorrect verification code, please try again", "changePINerror": "Failed to change employee PIN", "changePINSuccess": "Employee <strong>{{name}}</strong> PIN successfully changed", "verifiyEmail": {"resend": {"title": "Verification code has been sent", "description": "Please check email {{email}} to get your verification code"}, "success": "User verification with {{role}} access rights"}, "sendMajooTeamsAccessSuccess": "Employee <strong>{{employees}}</strong> successfully got access to majoo Teams", "moveSuccess": "Employee successfully moved", "terminateSuccess": "Employee successfully terminated", "failedDeleteHasSubordinates": "Cannot delete, this employee still has team members"}, "inputErrorMessage": {"email": "Email cannot be empty", "nId": "ID Card Number cannot be empty", "nIdLength": "NIN (National Identity Number) filled with 16 digits", "name": "Name cannot be empty", "employeeId": "ID Card Number cannot be empty", "access": "Access Rights cannot be empty", "outlet": "Outlet cannot be empty", "pinLength": "PIN uses 6 digits numbers", "pinLength2": "*PIN must use 6 digits numbers", "pinUnmatch": "*PIN doesn't match", "emailVerification": "Email verification required", "unverifiedEmail": "Unverified: Please verify email first", "placeOfBirth": "Place of birth cannot be empty", "dateOfBirth": "Date of birth cannot be empty", "gender": "Gender cannot be empty", "religion": "Religion cannot be empty", "maritalStatus": "Marital Status cannot be empty", "phone": "Phone number cannot be empty", "alamatKTP": "Address by ID Card cannot be empty", "npwp": "TIN Number cannot be empty", "bankName": "Bank Name cannot be empty", "bankAccount": "Account number cannot be empty", "bankOwnerName": "Bank Account Owned rekening cannot be empty", "endDate": "End date cannot be empty", "cantEmpty": " {{ field }} cannot be empty"}, "dataInputSelect": {"optionTerminate": {"resign": "Resign", "workTermination": "Work Termination"}}}, "banner": {"title": "Employee Data Import in Progress", "description": "The \"Import data\" button will be temporarily disabled until the employee data import process is complete"}}
import React, {
  useState, useEffect, useRef, useMemo,
} from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import moment from 'moment';
import {
  Box,
  Paper,
  Separator,
  Flex,
  Skeleton,
  Heading,
  InputSelect,
  Text,
  IconButton,
  Paragraph,
  ModalDialog,
  Image,
  DialogClose,
  Button,
  EditableTable,
  InputResourceList,
  ToastContext,
} from '@majoo-ui/react';
import { ChevronRightOutline, GearFilled, RefreshFilled } from '@majoo-ui/icons';
import { Trans, useTranslation } from 'react-i18next';
import CoreHOC from '../../core/CoreHOC';
import {
  column,
  widgetPaper,
  defaultPeriodOptions as _periodOptions,
  defaultPeriodOptionsYear as _periodOptionsYear,
  defaultCashFlowLegend as _cashFlowLegend,
  defaultCashFlowLegendEng as _cashFlowLegendEng,
  defaultProfitLossLegend as _profitLossLegend,
  defaultProfitLossLegendEng as _profitLossLegendEng,
  pieColors,
  CASH_STORAGE_KEY,
  GROUP,
} from './utils';
import { fillDataDaily, LineChart } from '../../components/chart';
import { colors } from '../../stitches.config';
import { useMediaQuery } from '../../utils/useMediaQuery';
import doodle from '../../../assets/images/doodle-integrated-outlet.png';
import { catchError, getAcronym } from '../../utils/helper';
import {
  getGraphProfitLoss, getGraphCashFlow, getBalanceSheet, getExpense, getCashBalance, getListCashBalance, getIsCustomizable,
} from '../../data/accounting';
import { BannerText } from '../../components/retina';
import TitleSection from './components/TitleSection';
import { dummyLine, dummyLineCash, dummyPie, dummySummary, dummyTable } from './dummyFinance';
import { OverlayWidget } from './components/OverlayWidget';
import { MiniModal } from './components/MiniModal';
import { CustomTooltip } from './components/chart/CustomTooltip';
import { WrapperTooltip } from './components/WrapperTooltip';
import { TextContent } from './components/TextContent';
import { SummarySheet } from './components/SummarySheet';
import { FinancePieChart } from './components/chart/FinancePieChart';
import { FinancePieModal } from './components/FinancePieModal';

const IS_DATAMART = false;
const DashboardFinance = (props) => {
  const {
    calendar,
    filterBranch,
    logo: avatarURL,
    namaUsaha,
    showProgress,
    hideProgress,
  } = props;

  const { t, i18n, ready } = useTranslation(['Keuangan/dashboardFinance', 'translation']);
  const currentLang = i18n.language;
  const {
    periodOptions, periodOptionsYear, cashFlowLegend, profitLossLegend,
  } = useMemo(() => ({
    periodOptions: t('periodOptions', { returnObjects: true, defaultValue: _periodOptions }),
    periodOptionsYear: t('periodOptionsYear', { returnObjects: true, defaultValue: _periodOptionsYear }),
    cashFlowLegend: currentLang === 'en' ? _cashFlowLegendEng : _cashFlowLegend,
    profitLossLegend: currentLang === 'en' ? _profitLossLegendEng : _profitLossLegend,
  }), [currentLang, ready]);

  const { addToast } = React.useContext(ToastContext);

  const date = [moment(calendar.start, 'DD/MM/YYYY').toDate(), moment(calendar.end, 'DD/MM/YYYY').toDate()];

  const startDate = date[0].setHours(0, 0, 0, 0);
  const endDate = date[1].setHours(0, 0, 0, 0);

  const [summarySheet, setSummarySheet] = useState({
    isGuide: false, isLoading: true, isError: false, data: { asset: 0, liability: 0, modal: 0 },
  });
  const [balance, setBalance] = useState({
    isGuide: false, isLoading: true, isError: false, data: [], isEdit: false,
  });
  const [expense, setExpense] = useState({
    isGuide: false, isLoading: true, isError: false, data: [], period: periodOptions[0],
  });
  const [profitLossChart, setProfitLossChart] = useState({
    isGuide: false, isLoading: true, isError: false, data: [], period: periodOptions[0], lastUpdate: moment(),
  });
  const [cashFlowChart, setCashFlowChart] = useState({
    isGuide: false, isLoading: true, isError: false, data: [], period: periodOptions[0],
  });
  const [isGuide, setIsGuide] = useState(false);
  const [openGuide, setOpenGuide] = useState({ modal: false });
  const [cashList, setCashList] = useState([]);
  const cacheCashList = useRef([]);
  const cacheChecked = useRef([]);
  const [checkedCash, setCheckedCash] = useState([]);
  const [isCustomizable, setIsCustomizable] = useState(false);
  const is3XL = useMediaQuery('(min-width: 1600px)');
  const dayPeriod = periodOptions[0];

  const avatarAcronym = getAcronym(namaUsaha).substring(0, 2);
  const [openDetail, setOpenDetail] = useState(false);

  const startGuide = () => {
    setIsGuide(true);
    setProfitLossChart(obj => ({ ...obj, isGuide: true, isLoading: false }));
    setCashFlowChart(obj => ({ ...obj, isGuide: true, isLoading: false }));
    setBalance(obj => ({ ...obj, isGuide: true, isLoading: false }));
    setSummarySheet(obj => ({ ...obj, isGuide: true, isLoading: false }));
    setExpense(obj => ({ ...obj, isGuide: true, isLoading: false }));
  };

  const exitGuide = () => {
    setIsGuide(false);
    setProfitLossChart(obj => ({ ...obj, isGuide: false }));
    setCashFlowChart(obj => ({ ...obj, isGuide: false }));
    setBalance(obj => ({ ...obj, isGuide: false }));
    setSummarySheet(obj => ({ ...obj, isGuide: false }));
    setExpense(obj => ({ ...obj, isGuide: false }));
  };

  const fetchProfitLossGraph = async (group) => {
    setProfitLossChart(s => ({
      ...s, isLoading: true,
    }));
    const _group = group || profitLossChart.period.value;
    try {
      const res = await getGraphProfitLoss({ group: _group, outlet_id: filterBranch });
      const data = _group === GROUP.DAY
        ? fillDataDaily(startDate, endDate, 'date', res.data, profitLossLegend)
        : res.data.map(d => ({
          ...d,
          date: moment(d.date, 'YYYY-MM-DD').toDate(),
        }));
      setProfitLossChart(s => ({
        ...s, isLoading: false, data, lastUpdate: res.last_update,
      }));
    } catch (e) {
      addToast({
        title: t('toast.error', { ns: 'translation' }),
        description: catchError(e),
        variant: 'failed',
      });
    } finally {
      setProfitLossChart(s => ({
        ...s, isLoading: false,
      }));
    }
  };

  const fetchCashFlowGraph = async (group) => {
    const _group = group || profitLossChart.period.value;
    setCashFlowChart(s => ({
      ...s, isLoading: true,
    }));
    try {
      const res = await getGraphCashFlow({ group: _group, outlet_id: filterBranch });
      const data = _group === 'day'
        ? fillDataDaily(startDate, endDate, 'date', res.data, cashFlowLegend)
        : res.data.map(d => ({
          ...d,
          date: moment(d.date, 'YYYY-MM-DD').toDate(),
        }));
      setCashFlowChart(s => ({
        ...s, data,
      }));
    } catch (e) {
      addToast({
        title: t('toast.error', { ns: 'translation' }),
        description: catchError(e),
        variant: 'failed',
      });
    } finally {
      setCashFlowChart(s => ({
        ...s, isLoading: false,
      }));
    }
  };

  const fetchSummarySheet = async () => {
    setSummarySheet(s => ({
      ...s, isLoading: true,
    }));
    try {
      const res = await getBalanceSheet({ outlet_id: filterBranch });
      setSummarySheet(s => ({
        ...s, data: res.data,
      }));
    } catch (e) {
      addToast({
        title: t('toast.error', { ns: 'translation' }),
        description: catchError(e),
        variant: 'failed',
      });
    } finally {
      setSummarySheet(s => ({
        ...s, isLoading: false,
      }));
    }
  };

  const fetchBalance = async (arr) => {
    setBalance(s => ({
      ...s, isLoading: true,
    }));
    if ((Array.isArray(arr) && arr.length === 0)) {
      setBalance(s => ({
        ...s, isLoading: false, data: [],
      }));
      return [];
    }
    const accountCodeList = Array.isArray(arr) ? arr.join(',') : arr;
    try {
      const res = await getCashBalance({ account_code_list: accountCodeList, outlet_id: filterBranch });
      setBalance(s => ({
        ...s, data: res.data,
      }));
      const _arr = res.data.map(m => ({ label: `${m.account.code} - ${m.account.name}`, value: m.account.code }));
      return _arr;
    } catch (e) {
      addToast({
        title: t('toast.error', { ns: 'translation' }),
        description: catchError(e),
        variant: 'failed',
      });
      return [];
    } finally {
      setBalance(s => ({
        ...s, isLoading: false,
      }));
    }
  };

  const fetchExpense = async (group) => {
    setExpense(s => ({
      ...s, isLoading: true,
    }));
    const _group = group || expense.period.value;
    try {
      const res = await getExpense({ group: _group, outlet_id: filterBranch });
      setExpense(s => ({
        ...s,
        data: res.data.map((r, index) => ({ ...r, color: pieColors[index % pieColors.length] })),
      }));
    } catch (e) {
      addToast({
        title: t('toast.error', { ns: 'translation' }),
        description: catchError(e),
        variant: 'failed',
      });
    } finally {
      setExpense(s => ({
        ...s, isLoading: false,
      }));
    }
  };

  const fetchListCashBalance = async () => {
    try {
      const res = await getListCashBalance({ outlet_id: '' });
      const remap = res.data.map(d => ({ value: d.code, label: `${d.code} - ${d.name}` }));
      return remap;
    } catch (e) {
      addToast({
        title: t('toast.error', { ns: 'translation' }),
        description: catchError(e),
        variant: 'failed',
      });
      return [];
    }
  };

  const fetchIsCustomizable = async () => {
    try {
      const res = await getIsCustomizable();
      setIsCustomizable(res.data);
    } catch (e) {
      addToast({
        title: t('toast.error', { ns: 'translation' }),
        description: catchError(e),
        variant: 'failed',
      });
    }
  };

  const fetchData = async () => {
    showProgress();
    try {
      await Promise.allSettled([
        fetchProfitLossGraph(),
        fetchCashFlowGraph(),
        fetchSummarySheet(),
        fetchExpense(),
        fetchIsCustomizable(),
      ]);
    } catch (error) {
      addToast({
        title: t('toast.error', { ns: 'translation' }),
        description: catchError(error),
        variant: 'failed',
      });
    } finally {
      hideProgress();
    }
  };

  const getPeriodDate = () => {
    switch (expense.period.value) {
      case 'day':
        return moment().format('DD MMMM YYYY');
      case 'week': {
        const firstDayofWeek = moment().startOf('isoWeek');
        const lastDayofWeek = moment().endOf('isoWeek');
        return `${firstDayofWeek.format('DD MMM')} - ${lastDayofWeek.format('DD MMM YYYY')}`;
      }
      case 'month': {
        return moment().format('MMMM YYYY');
      }
      case 'year': {
        return moment().format('YYYY');
      }
      default:
        return '';
    }
  };

  useEffect(async () => {
    fetchData();

    const localCash = localStorage.getItem(CASH_STORAGE_KEY);
    const accountList = await fetchListCashBalance();
    cacheCashList.current = accountList;
    setCashList(accountList);
    if (localCash === null) {
      const res = await fetchBalance('');
      localStorage.setItem(CASH_STORAGE_KEY, res.map(m => m.value));
      setCheckedCash(res);
      return;
    }
    if (localCash === '') {
      let res = '';
      if (!filterBranch) {
        res = await fetchBalance('');
      }
      localStorage.setItem(CASH_STORAGE_KEY, res ? res.map(m => m.value) : '');
      setCheckedCash(res);
      return;
    }
    const localArr = localCash.split(',');
    if (localCash && localArr.length > 0) {
      const res = await fetchBalance(localArr);
      const resValue = res.map(m => m.value);
      localStorage.setItem(CASH_STORAGE_KEY, resValue);
      setCheckedCash(res);
      cacheChecked.current = res;
    }
  }, [filterBranch]);

  const trigger = (fn) => {
    if (!isGuide) return {};
    return {
      onMouseLeave: () => fn(s => ({ ...s, isGuide: true })),
    };
  };

  const checkedValue = checkedCash.map(m => m.value);
  const filteredCashList = cashList.filter(f => !checkedValue.includes(f.value));
  return (
    <Flex direction="column" gap={5} css={{ mb: '$cozy' }}>
      <Paper
        responsive
        css={{
          padding: 0,
          display: 'flex',
          flexDirection: 'column',
          gap: '$compact',
          '@md': {
            padding: '$compact $cozy',
          },
        }}
      >
        <TitleSection
          isGuide={isGuide}
          title={t('title', 'Keuangan')}
          onClickButton={() => {
            setOpenGuide(s => ({ ...s, modal: true }));
          }
          }
          onClickButtonExit={exitGuide}
          t={t}
        />
        <BannerText css={{ my: 0 }} />
        <Separator css={{ my: 4 }} />
        <Flex direction="column" gap={5} css={{ '@md': { padding: '$compact', borderRadius: '$lg', border: '1px solid $gray150' } }}>
          <Flex
            direction="column"
            gap={5}
            css={{ position: 'relative', '& > div:nth-child(3) > div > div:nth-child(3)': { gridColumn: '1 / 3' }, '& > div:nth-child(3)': { gap: 0, '@md': { gap: '$compact' } } }}
            {...trigger(setProfitLossChart)}
          >
            <Flex justify="between" css={{ flexDirection: 'column', gap: '$compact', '@md': { flexDirection: 'row', gap: 0 } }}>
              <Heading as="h2" heading="sectionTitle">{t('profitLoss.title', 'Laba Rugi')}</Heading>
              <InputSelect
                size="sm"
                css={{ width: 124 }}
                value={isGuide ? dayPeriod : periodOptions.find(f => f.value === profitLossChart.period.value)}
                option={periodOptions}
                onChange={(opt) => {
                  setProfitLossChart(obj => ({ ...obj, period: opt }));
                  fetchProfitLossGraph(opt.value);
                }}
              />
            </Flex>
            <Separator css={{ display: 'none', my: 4, '@md': { display: 'block' } }} />
            {profitLossChart.isLoading ? (
              <Skeleton
                patternCount={1}
                css={{
                  maxHeight: '346px',
                }}
              />
            ) : (
              <LineChart
                data={isGuide ? dummyLine.map((dummy, index) => ({ ...dummy, date: moment().startOf('month').add(index, 'd').toDate() })) : profitLossChart.data}
                legendData={profitLossLegend}
                customXAxisFormat={(tick) => {
                  const _period = isGuide ? dayPeriod.value : profitLossChart.period.value;
                  if (_period === GROUP.WEEK) {
                    const current = moment(tick);
                    const next = moment(tick).add(6, 'day');
                    return `${current.format('D MMM')} - ${next.format('D MMM')}`;
                  }
                  return new Intl.DateTimeFormat(
                    currentLang, { day: '2-digit' },
                  ).format(new Date(tick));
                }}
                period={isGuide ? dayPeriod.value : profitLossChart.period.value}
                xAxisKey="date"
                width={is3XL ? '1150px' : '950px'}
                height="292px"
                customTooltip={isGuide ? CustomTooltip : (undefined)
                }
              />
            )}
            {profitLossChart.isGuide ? (
              <OverlayWidget>
                <MiniModal
                  title={t('profitLoss.titleGuide')}
                  description={t('profitLoss.description')}
                  width="422px"
                  onClick={() => { setProfitLossChart(s => ({ ...s, isGuide: false })); }}
                />
              </OverlayWidget>
            ) : (null)}
          </Flex>
          <Separator css={{
            my: 4, position: 'relative', left: 0, width: '100% !important', '@md': { left: '-16px', width: 'calc(100% + 32px) !important' },
          }}
          />
          <Flex
            direction="column"
            gap={5}
            css={{ position: 'relative', '& > div:nth-child(2)': { gap: 0, '@md': { gap: '$compact' } } }}
            {...trigger(setCashFlowChart)}
          >
            <Flex justify="between" css={{ flexDirection: 'column', gap: '$compact', '@md': { flexDirection: 'row', gap: 0 } }}>
              <Heading as="h2" heading="sectionTitle">{t('cashFlow.title', 'Arus Kas')}</Heading>
              <InputSelect
                size="sm"
                css={{ width: 124 }}
                value={periodOptions.find(f => f.value === cashFlowChart.period.value)}
                option={periodOptions}
                onChange={(opt) => {
                  setCashFlowChart(obj => ({ ...obj, period: opt }));
                  fetchCashFlowGraph(opt.value);
                }}
              />
            </Flex>
            {cashFlowChart.isLoading ? (
              <Skeleton
                patternCount={1}
                css={{
                  maxHeight: '346px',
                }}
              />
            ) : (
              <LineChart
                data={isGuide ? dummyLineCash.map((dummy, index) => ({ ...dummy, date: moment().startOf('month').add(index, 'd').toDate() })) : cashFlowChart.data}
                legendData={cashFlowLegend}
                customXAxisFormat={(tick) => {
                  const _period = isGuide ? dayPeriod.value : cashFlowChart.period.value;
                  if (_period === GROUP.WEEK) {
                    const current = moment(tick);
                    const next = moment(tick).add(6, 'day');
                    return `${current.format('D MMM')} - ${next.format('D MMM')}`;
                  }
                  return new Intl.DateTimeFormat(
                    currentLang, { day: '2-digit' },
                  ).format(new Date(tick));
                }}
                period={isGuide ? dayPeriod.value : cashFlowChart.period.value}
                xAxisKey="date"
                width={is3XL ? '1150px' : '950px'}
                height="292px"
                customTooltip={isGuide ? CustomTooltip : (undefined)}
              />
            )}
            {cashFlowChart.isGuide ? (
              <OverlayWidget>
                <MiniModal
                  title={t('cashFlow.titleGuide')}
                  description={t('cashFlow.description')}
                  width="422px"
                  onClick={() => { setCashFlowChart(s => ({ ...s, isGuide: false })); }}
                />
              </OverlayWidget>
            ) : (null)}
          </Flex>
        </Flex>
      </Paper>
      <Box
        css={{
          display: 'flex',
          flexDirection: 'column',
          width: '100%',
          gap: '$compact',
          minHeight: 224,
          mt: 48,
          '@md': {
            mt: 0,
            maxHeight: 'auto',
            flexDirection: 'row',
          },
        }}
      >
        {balance.isLoading ? (
          <Skeleton
            patternCount={1}
            css={{
              maxHeight: '371px',
              width: '100%',
              '@md': { width: '33%' },
            }}
          />
        ) : (
          <Paper
            css={widgetPaper}
            {...trigger(setBalance)}
          >
            <Flex justify="between">
              <Flex direction="column">
                <Heading as="h2" heading="sectionTitle">{t('balance.title', 'Saldo Kas')}</Heading>
                <Text variant="label">{`Per ${moment().format('DD MMMM YYYY')}`}</Text>
              </Flex>
              <WrapperTooltip
                condition={isGuide}
                label={(
                  <TextContent css={{ maxWidth: 204 }} title={t('settings')}>
                    {t('balance.max')}
                  </TextContent>
                )}
              >
                <IconButton disabled={!isCustomizable} onClick={() => { if (!isGuide) setBalance(s => ({ ...s, isEdit: true })); }}>
                  <GearFilled color={isCustomizable ? colors.iconGreen : colors.iconGray} />
                </IconButton>
              </WrapperTooltip>
            </Flex>
            <Separator css={{ my: 4 }} />
            {balance.isEdit ? (
              <Flex direction="column" gap={5} css={{ height: '100%' }}>
                <InputResourceList
                  css={{ '& > :nth-child(2)': { maxHeight: 150 }, '& > :nth-child(3)': { display: 'none' } }}
                  data={filteredCashList}
                  value={checkedCash}
                  onQueryChange={query => (query.length > 0
                    ? setCashList(() => {
                      const filtered = cacheCashList.current.filter((item) => {
                        const reg = new RegExp(query, 'i');
                        return item.label.match(reg);
                      });
                      return filtered;
                    })
                    : setCashList(cacheCashList.current))
                  }
                  onItemCheck={(item) => {
                    setCheckedCash((current) => {
                      const result = [...current, item];
                      return result;
                    });
                  }}
                  onItemUncheck={(item) => {
                    setCheckedCash(current => current.filter(
                      currItem => currItem.value !== item.value,
                    ));
                  }}
                  enabledItems={checkedCash.length > 5 ? checkedValue : undefined}
                />
                <Separator css={{ mt: 'auto' }} />
                <Flex justify="between">
                  <Button
                    size="sm"
                    buttonType="secondary"
                    onClick={() => {
                      setBalance(s => ({ ...s, isEdit: false }));
                      setCheckedCash(cacheChecked.current);
                      setCashList(cacheCashList.current);
                    }}
                  >
                    {t('label.back', { ns: 'translation', defaultValue: 'Kembali' })}
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => {
                      cacheChecked.current = checkedCash;
                      const _list = checkedCash.map(m => m.value);
                      localStorage.setItem(CASH_STORAGE_KEY, _list);
                      setBalance(s => ({ ...s, isEdit: false }));
                      fetchBalance(_list);
                    }}
                    disabled={checkedCash.length > 5}
                  >
                    {t('label.save', { ns: 'translation', defaultValue: 'Simpan' })}
                  </Button>
                </Flex>
              </Flex>
            ) : (
              <EditableTable
                css={{ padding: 0, '& span': { width: '100%' } }}
                totalData={isGuide ? dummyTable.length : balance.data.length}
                data={isGuide ? dummyTable : balance.data}
                rowHeight={44}
                columns={column(isGuide, t)}
                isMobileLayout={false}
              />
            )}
            {balance.isGuide ? (
              <OverlayWidget
                size="sm"
              >
                <MiniModal
                  title={t('balance.title')}
                  description={t('balance.description')}
                  width="277px"
                  onClick={() => { setBalance(s => ({ ...s, isGuide: false })); }}
                />
              </OverlayWidget>
            ) : (null)}
          </Paper>
        )
        }

        {summarySheet.isLoading ? (
          <Skeleton
            patternCount={1}
            css={{
              maxHeight: '371px',
              width: '100%',
              '@md': { width: '33%' },
            }}
          />
        ) : (
          <Paper
            css={widgetPaper}
            {...trigger(setSummarySheet)}
          >
            <Flex justify="between">
              <Flex direction="column">
                <Heading as="h2" heading="sectionTitle">{t('summary.title', 'Neraca')}</Heading>
                <Text variant="label">{`Per ${moment().format('DD MMMM YYYY')}`}</Text>
              </Flex>
              {IS_DATAMART ? (
                <IconButton onClick={fetchSummarySheet}>
                  <RefreshFilled color={colors.iconGreen} />
                </IconButton>
              ) : null}
            </Flex>
            <Separator css={{ my: 4 }} />
            <SummarySheet isDatamart={IS_DATAMART} isGuide={isGuide} lastUpdate={profitLossChart.lastUpdate} data={isGuide ? dummySummary : summarySheet.data} t={t} />
            {summarySheet.isGuide ? (
              <OverlayWidget
                size="sm"
              >
                <MiniModal
                  title={t('summary.title')}
                  description={t('summary.description')}
                  width="277px"
                  onClick={() => { setSummarySheet(s => ({ ...s, isGuide: false })); }}
                />
              </OverlayWidget>
            ) : (null)}
          </Paper>
        )}

        {expense.isLoading ? (
          <Skeleton
            patternCount={1}
            css={{
              maxHeight: '371px',
              width: '100%',
              '@md': { width: '33%' },
            }}
          />
        ) : (
          <Paper
            css={widgetPaper}
            {...trigger(setExpense)}
          >
            <Flex justify="between">
              <Flex direction="column">
                <Heading as="h2" heading="sectionTitle">{t('expense.title', 'Biaya')}</Heading>
                <Text variant="label">{getPeriodDate()}</Text>
              </Flex>
              <InputSelect
                size="sm"
                css={{ width: 124 }}
                value={isGuide ? dayPeriod : periodOptionsYear.find(f => f.value === expense.period.value)}
                option={periodOptionsYear}
                onChange={(opt) => {
                  setExpense(obj => ({ ...obj, period: opt }));
                  fetchExpense(opt.value);
                }}
              />
            </Flex>
            <FinancePieChart key={`${isGuide}-pie-finance-${expense.data.length}-${expense.period}`} isGuide={isGuide} data={isGuide ? dummyPie : expense.data} avatarURL={avatarURL} avatarAcronym={avatarAcronym} t={t} />
            {isGuide || (!isGuide && expense.data.length > 0) ? (
              <Box css={{
                display: 'flex', justifyContent: 'center', flexDirection: 'column', gap: '$compact',
              }}
              >
                <Button onClick={() => setOpenDetail(true)} size="sm" buttonType="ghost" rightIcon={<ChevronRightOutline color="currentColor" />}>{t('expense.moreDetail', 'Lihat Detail Biaya')}</Button>
                <Text align="center" variant="label">
                  {t('expense.max', 'Data yang ditampilkan adalah 5 biaya terbesar')}
                </Text>
              </Box>
            ) : null}
            <FinancePieModal open={openDetail} onOpenChange={setOpenDetail} key={`${isGuide}-pie-finance`} isGuide={isGuide} data={isGuide ? dummyPie : expense.data} avatarURL={avatarURL} avatarAcronym={avatarAcronym} t={t} />
            {expense.isGuide ? (
              <OverlayWidget
                size="sm"
              >
                <MiniModal
                  title={t('expense.title')}
                  description={t('expense.description')}
                  width="277px"
                  onClick={() => { setExpense(s => ({ ...s, isGuide: false })); }}
                />
              </OverlayWidget>
            ) : (null)}
          </Paper>
        )}
      </Box>
      <ModalDialog size="lg" open={openGuide.modal} onOpenChange={val => setOpenGuide(s => ({ ...s, modal: val }))}>
        <ModalDialog.Title>
          {t('modal.title')}
        </ModalDialog.Title>
        <ModalDialog.Content>
          <Flex direction="column" justify="center" align="center" css={{ margin: 'auto', padding: '$cozy' }}>
            <Image src={doodle} width="200px" height="200px" alt="Doodle Panduan" />
            <Flex direction="column" gap={3}>
              <Heading align="center" as="h3" heading="pageTitle">{t('modal.heading')}</Heading>
              <Paragraph align="center" paragraph="shortContentRegular">
                <Trans t={t} i18nKey="modal.content" />
              </Paragraph>
            </Flex>
          </Flex>
        </ModalDialog.Content>
        <ModalDialog.Footer>
          <DialogClose asChild>
            <Button onClick={startGuide}>{t('modal.start')}</Button>
          </DialogClose>
        </ModalDialog.Footer>
      </ModalDialog>
    </Flex>
  );
};

DashboardFinance.propTypes = {
  filterBranch: PropTypes.string,
  calendar: PropTypes.shape({
    start: PropTypes.string,
    end: PropTypes.string,
    rangeLimit: PropTypes.number,
    onchange: PropTypes.func,
  }).isRequired,
  addNotification: PropTypes.func.isRequired,
  logo: PropTypes.string,
  namaUsaha: PropTypes.string,
  hideProgress: PropTypes.func,
  showProgress: PropTypes.func,
};

DashboardFinance.defaultProps = {
  filterBranch: '',
  logo: '',
  namaUsaha: '',
  hideProgress: () => { },
  showProgress: () => { },
};

function mapStateToProps(state) {
  return {
    logo: state.user.profile.user_usaha_logo_path,
    namaUsaha: state.user.profile.user_usaha_name,
    namaUser: state.accountInfo.accountInfoResult.user_name,
  };
}

export default CoreHOC(connect(mapStateToProps)(DashboardFinance));

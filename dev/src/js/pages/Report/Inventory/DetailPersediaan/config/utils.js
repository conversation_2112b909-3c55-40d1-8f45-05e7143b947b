import React from 'react';
import {
 <PERSON><PERSON>, <PERSON>ltip, DateColumn, Heading,
} from '@majoo-ui/react';
import { Trans } from 'react-i18next';
import { CircleInfoOutline } from '@majoo-ui/icons';
import {
    downloadReportDetailPersediaan,
} from './api';
import { currency, formatThousandSeparator } from '../../../../../utils/helper';
import { NullableColumn, PriceColumn } from '../../../../../components/retina';

export const FilterOption = [
    'outlet',
    'product',
];

export const MAX_SELECTED_ITEM = {
    outlet: 20,
};

const colWidth = 137;

export const tableColumns = (t, getContentTranslation) => [
    {
        id: 'outlet',
        Header: 'Outlet',
        accessor: 'outlet_name',
        Cell: (propsRow) => {
            const { row, value } = propsRow;
            return value || '';
        },
        unsortable: true,
        sticky: 'left',
        shadow: true,
        colWidth,
    },
    {
        id: 'transDate',
        Header: t('table.transDate', 'Tanggal Transaksi'),
        accessor: 'transaction_date',
        unsortable: true,
        Cell: ({ value }) => <DateColumn value={value} customFormat="DD MMMM YYYY HH:mm" />,
        colWidth,
    },
    {
        id: 'transaction',
        Header: t('table.transaction', 'Transaksi'),
        accessor: 'transaction_name',
        unsortable: true,
        Cell: ({ value }) => <NullableColumn value={getContentTranslation(value)} />,
        colWidth,
    },
    {
        id: 'transNo',
        Header: t('table.transNo', 'No Transaksi'),
        accessor: 'transaction_no',
        unsortable: true,
        colWidth,
    },
    {
        id: 'transType',
        Header: t('table.quantity', 'Kuantitas'),
        accessor: 'transaction_quantity',
        unsortable: true,
        Cell: ({ value }) => currency({
            value,
            type: '',
            convertMinus: true,
            decimal: value % 1 !== 0,
        }),
        colWidth,
    },
    {
        id: 'unit',
        Header: t('table.unit', 'Satuan'),
        accessor: 'transaction_unit_name',
        unsortable: true,
        Cell: ({ value }) => <NullableColumn value={getContentTranslation(value)} />,
        colWidth,
    },
    {
        id: 'sellingPrice',
        Header: t('table.price', 'Harga Jual/Beli'),
        accessor: 'transaction_selling_price',
        unsortable: true,
        Cell: PriceColumn,
        colWidth: 200,
    },
    {
        id: 'transStock',
        Header: t('table.stock', 'Stok'),
        accessor: 'transaction_stock',
        unsortable: true,
        Cell: ({ value }) => formatThousandSeparator(value),
        colWidth,
    },
    {
        id: 'averagePrice',
        Header:
            <Flex gap={3} align="center" css={{ width: colWidth }}>
                <Heading heading="headerTable">
                    {t('table.averagePrice', 'HARGA MODAL')}
                </Heading>
                <Tooltip
                    label={t('table.capitalTooltip')}
                    side="top"
                >
                    <CircleInfoOutline size={16} />
                </Tooltip>
            </Flex>,
        accessor: 'transaction_basic_price',
        unsortable: true,
        Cell: PriceColumn,
        colWidth: 300,
    },
    {
        id: 'stockPrice',
        Header: t('table.totalInventoryValue', 'Total Nilai Persediaan'),
        accessor: 'transaction_total_stock_price',
        unsortable: true,
        Cell: PriceColumn,
        colWidth: 300,
    },
];

export const downloadReport = async ({
    payload, showProgress, hideProgress, t, addNotification,
    setDialogInfoExport, setShowExportBanner,
}) => {
    showProgress();
    try {
        await downloadReportDetailPersediaan(payload);
        addNotification({
            title: t('toast.success', { ns: 'translation' }),
            message: (
                <Trans t={t} i18nKey="toast.exportSuccess">
                    <strong>Laporan Detail Persediaan</strong>
                    {' '}
                    berhasil diekspor
                </Trans>
            ),
            level: 'success',
        });
        setShowExportBanner(true);
    } catch (error) {
        if (error.cause && error.cause.status && error.cause.status.code) {
            if (String(error.cause.status.code) === '42200002') {
                setDialogInfoExport({
                    isOpen: true,
                    state: {
                        title: (
                            <Trans
                                t={t}
                                i18nKey="translation:label.exportReport"
                                defaults="Ekspor Laporan"
                            />
                        ),
                        description: (
                            <Trans
                                t={t}
                                i18nKey="exportModalInProgressDesc"
                                defaults="<strong>Laporan Detail Persediaan</strong> sedang diproses. Data dapat diunduh setelah selesai diproses."
                                components={{ strong: <b /> }}
                            />
                        ),
                        confirmLabel: t('label.close', { ns: 'translation' }),
                        singleButton: true,
                    },
                });
                return;
            }
        }
        addNotification({
            title: t('toast.error', { ns: 'translation' }),
            message: (
                <Trans t={t} i18nKey="toast.exportFailed">
                    <strong>Laporan Detail Persediaan</strong>
                    {' '}
                    gagal diekspor
                </Trans>
            ),
            level: 'error',
        });
    } finally {
        hideProgress();
    }
};

export const itemTypes = [
    {
        value: 0,
        id: 'allType',
        name: 'Semua Jenis',
    },
    {
        value: 1,
        id: 'finishedMaterial',
        name: 'Barang Jadi',
    },
    {
        value: 2,
        id: 'rawMaterial',
        name: 'Bahan Baku',
    },
];

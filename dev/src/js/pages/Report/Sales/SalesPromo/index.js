import React, {
  useState, useRef, useEffect, useCallback,
} from 'react';
import { connect } from 'react-redux';
import * as PropTypes from 'prop-types';
import moment from 'moment';
import queryString from 'query-string';
import {
  Paper,
  useToggle,
  Flex,
  Separator,
  Box,
  InputDatePicker,
  ADVANCE_PICKER,
  InputSelect,
  // LineChart,
  InputSearchbox,
  Table,
  AlertDialog,
  Button,
  PageDialog,
  FormGroup,
  FormLabel,
  DialogClose,
  InputDateRange,
} from '@majoo-ui/react';
import { foundations } from '@majoo-ui/core';
import { debounce, uniqBy } from 'lodash';
import { Trans, useTranslation } from 'react-i18next';

import { DownloadOutline, FilterOutline } from '@majoo-ui/icons';
import CoreHOC from '../../../../core/CoreHOC';
import {
  catchError,
  numSeparator,
  formatCurrency,
  sortArr,
  resetDateRange<PERSON><PERSON>per,
} from '../../../../utils/helper';

// new implemented component
import * as reportApi from '../../../../data/reports';
import { tableColumns } from './settings/table';
import { TitleSection } from './component/TitleSection';
import {
  fillDataDaily,
  fillDataHourly,
  fillDataMonthly,
  fillDataWeekly,
  formatDay,
  formatHour,
  formatMonth,
  formatYear,
  LineChart,
} from '../../../../components/chart';
import { useMediaQuery } from '../../../../utils/useMediaQuery';
import { BannerText, CardWrapper, SalesIncreasement } from '../../../../components/retina';
import useResetDateRange from '../../../../hooks/useResetDateRange';
import ExportReport from '../../../../components/retina/export/ExportReport';
import useDidMountEffect from '../../../../utils/useDidMountEffect';

const { colors } = foundations;

function ReportPromo(props) {
  const {
    calendar, assignCalendar, router, hideProgress, filterBranch, addNotification, showProgress,
  } = props;
  const { t, i18n: { language: lang } } = useTranslation(['Report/Sales/promo', 'translation']);
  const isMobile = useMediaQuery('(max-width: 767px)');
  const isTablet = useMediaQuery('(max-width: 1024px)');
  const overviewRef = useRef();
  moment().locale(lang);

  const chartColor = Object.keys(colors)
    .filter(key => key.includes('chart'))
    .reduce((prev, current) => [...prev, colors[current]], []);

  const [date, setDate] = useState([
    moment(calendar.start, 'DD/MM/YYYY').toDate(),
    moment(calendar.end, 'DD/MM/YYYY').toDate(),
  ]);

  const [tempCal, setTempCal] = useState([
    moment(calendar.start, 'DD/MM/YYYY').toDate(),
    moment(calendar.end, 'DD/MM/YYYY').toDate(),
  ]);

  const [openFilter, setOpenFilter] = useState(false);
  const [search, setSearch] = useState('');
  const [isLoading, setIsLoading] = useToggle(false);
  const [increasementNominal, setIncreasementNominal] = useState(0);

  const optFilter = [
    {
      name: t('translation:period.hour', { defaultValue: 'Jam' }),
      value: 'hour',
      isDisabled: true,
    },
    {
      name: t('translation:period.day', { defaultValue: 'Hari' }),
      value: 'day',
    },
    {
      name: t('translation:period.week', { defaultValue: 'Minggu' }),
      value: 'week',
    },
    {
      name: t('translation:period.month', { defaultValue: 'Bulan' }),
      value: 'month',
    },
    {
      name: t('translation:period.year', { defaultValue: 'Tahun' }),
      value: 'year',
    },
  ];

  const [period, setPeriod] = useState('day');
  const [tempPeriod, setTempPeriod] = useState('day');
  const [summaryData, setSummaryData] = useState({});
  const [tableData, setTableData] = useState([]);
  const [chartData, setChartData] = useState({
    initChart: [],
    filteredChart: [],
    dataCompare: [],
    topCompare: [],
    chartLegend: [],
  });
  const [metaData, setMetaData] = useState({
    limit: 10,
    page: 0,
    total: 0,
    filterKeyword: '',
    filterDate: [
      moment(calendar.start, 'DD/MM/YYYY').toDate(),
      moment(calendar.end, 'DD/MM/YYYY').toDate(),
    ],
  });
  const [dialogInfoExport, setDialogInfoExport] = useState({
    isDialog: false,
    state: {
      title: '',
      description: '',
      btnCloseOnly: false,
    },
  });
  const [dateRangeKey, resetDateRange] = useResetDateRange();

  const changeDate = (value) => {
    const { newStartDate, newEndDate, isForceReset } = resetDateRangeHelper(value[0], value[1], 90);
    if (isForceReset) {
      addNotification({
        title: t('toast.errorMaxDate', 'Terjadi Kesalahan!', { ns: 'translation' }),
        description: t('toast.errorMaxDateRange', 'Maksimum rentang waktu yang dapat dipilih: 3 bulan', { ns: 'translation' }),
        level: 'pending',
      });
      assignCalendar(newStartDate, newEndDate);
      setDate([moment(newStartDate, 'DD-MM-YYYY').toDate(), moment(newEndDate, 'DD-MM-YYYY').toDate()]);
    } else {
      assignCalendar(value[0], value[1]);
      setDate(value);
    }
  };

  const goToOverviewSection = () => overviewRef.current.scrollIntoView({ behavior: 'smooth' });

  const handleDetail = detailRow => router.push(
    `/laporan/penjualan/promo/detail/${detailRow.value}/${detailRow.row.original.date}/${detailRow.row.original.id}/${period}`,
  );

  const handleAutoScrollToOverview = () => {
    const params = queryString.parse(window.location.search);

    if (Object.keys(params).length > 0 && !!params.is_go_to_overview) {
      goToOverviewSection();
    }
  };

  const fetchReportV2 = async () => {
    setIsLoading(true);
    try {
      const payload = {
        start_date: moment(date[0], 'DD-MM-YYYY').format('YYYY-MM-DD'),
        end_date: moment(date[1], 'DD-MM-YYYY').format('YYYY-MM-DD'),
        limit: metaData.limit,
        page: metaData.page + 1,
        time_period: period,
        search,
        sort: metaData.sort || null,
        order: metaData.order || null,
        id_outlet: filterBranch,
      };

      Object.keys(payload).forEach((key) => {
        if (payload[key] === null || payload[key] === '') {
          delete payload[key];
        }
      });

      const res = await reportApi.getSalesPromo(payload);
      payload.limit = res.meta.total;
      const resChart = await reportApi.getSalesPromoGraph(payload);
      if (!res.status) {
        throw new Error(res.msg);
      }
      if (res.status) {
        const newTableData = res.data.map(item => ({
          ...item,
          uniqId: item.key + item.type,
        }));

        setTableData(newTableData);
        setMetaData({
          ...metaData,
          total: res.meta.total,
        });
        const summaryRes = await reportApi.getSalesPromoSummary(payload);
        setSummaryData(summaryRes.data);

        const newData = resChart.data.map(item => ({
          ...item,
          uniqId: item.key + item.type,
        }));

        const highestData = sortArr(newData, 'total', 'desc');
        const uniqData = uniqBy(highestData, 'uniqId');
        const dataComparator = uniqData.map(item => ({
          name: item.name,
          value: item.uniqId,
        }));
        const idToCompare = uniqData.map(item => item.uniqId).slice(0, 5);
        setChartData({
          ...chartData,
          initChart: newData,
          filteredChart: newData,
          dataCompare: dataComparator,
          topCompare: idToCompare,
        });
      }

      const payloadSummary = {
        start_date: moment().startOf('month').format('YYYY-MM-DD'),
        end_date: moment().endOf('month').format('YYYY-MM-DD'),
        ...filterBranch && { id_outlet: filterBranch },
      };

      const promoSummary = await reportApi.getSalesPromoSummary(payloadSummary);
      setIncreasementNominal(promoSummary.data.transaction_total);
    } catch (error) {
      addNotification({
        level: 'error',
        title: t('translation:toast.error', { defaultValue: 'Gagal!' }),
        message: catchError(error),
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const { initChart: grafikData, topCompare } = chartData;

    if (grafikData.length === 0) {
      setChartData({
        ...chartData,
        chartData: [],
        chartLegend: [],
        comparatorOptions: [],
      });
      return;
    }
    const lineChartLegend = topCompare.map((current, index) => {
      const transaction = grafikData.find(row => row.uniqId === current);
      return {
        dataKey: transaction.name,
        name: transaction.name,
        color: chartColor[index % chartColor.length],
        isComparator: true,
        value: transaction.uniqId,
      };
    });
    const getDataPerCashier = grafikData.reduce((prev, current) => {
      const match = prev.find(p => p.date === current.date);
      // moment(p.date).isSame(moment(current.date))
      if (match) {
        Object.assign(match, {
          ...{ [current.name]: current.total },
        });
      } else {
        prev.push({
          ...current,
          date: current.date,
          monday_date: current.monday_date,
          [current.name]: current.total,
        });
      }
      return prev;
    }, []);
    let newChartData = [];
    if (period === 'jam') {
      newChartData = fillDataHourly(
        date[0],
        date[1],
        'date',
        getDataPerCashier,
        lineChartLegend,
      );
    } else if (period === 'day') {
      newChartData = fillDataDaily(
        date[0],
        date[1],
        'date',
        getDataPerCashier,
        lineChartLegend,
      );
    } else if (period === 'week') {
      newChartData = fillDataWeekly(
        date[0],
        date[1],
        'date',
        getDataPerCashier,
        lineChartLegend,
      );
    } else if (period === 'month') {
      newChartData = fillDataMonthly(
        date[0],
        date[1],
        'date',
        getDataPerCashier,
        lineChartLegend,
      );
    } else {
      newChartData = getDataPerCashier;
    }

    setChartData({
      ...chartData,
      filteredChart: newChartData || [],
      chartLegend: lineChartLegend || [],
    });
  }, [
    // props.report,
    // table.search,
    // table.tableData,
    chartData.topCompare,
    // chart.initChart,
    // calendar,
    // dataType,
    // departement,
  ]);

  useDidMountEffect(() => {
    setMetaData({
      ...metaData,
      page: 0,
      filterKeyword: search,
    });
  }, [search]);

  useDidMountEffect(() => {
    setMetaData({
      ...metaData,
      page: 0,
      filterDate: date,
    });
  }, [date]);

  useEffect(() => {
    hideProgress();
    fetchReportV2();
    // fetchReportChart();
  }, [
    filterBranch,
    metaData.page,
    metaData.limit,
    metaData.sort,
    metaData.order,
    metaData.filterKeyword,
    metaData.filterDate,
    period,
  ]);

  useEffect(() => {
    handleAutoScrollToOverview();
  }, []);

  useEffect(() => {
    if (isLoading) {
      showProgress();
    } else {
      hideProgress();
    }
  }, [isLoading]);


  const handleXaxis = (_date) => {
    switch (period) {
      case 'hour':
        return formatHour(_date);
      case 'day':
        return formatDay(_date);
      case 'week':
        return formatDay(_date);
      case 'month':
        return formatMonth(_date);
      case 'year':
        return formatYear(_date);
      default:
        throw new Error();
    }
  };

  const handleComparator = useCallback(
    (value) => {
      switch (value.type) {
        case 'ADD':
          return setChartData({
            ...chartData,
            topCompare: [...chartData.topCompare, value.payload.value],
          });
        case 'DELETE':
          return setChartData({
            ...chartData,
            topCompare: chartData.topCompare.filter(
              item => item !== value.payload.value,
            ),
          });
        default:
          return value;
      }
    },
    [chartData.topCompare],
  );

  const downloadLaporan = async (type = 'xlsx') => {
    try {
      showProgress();
      const payload = {
        start_date: moment(date[0], 'DD-MM-YYYY').format('YYYY-MM-DD'),
        end_date: moment(date[1], 'DD-MM-YYYY').format('YYYY-MM-DD'),
        ...(filterBranch && { outlet_id: Number(filterBranch) }),
        ...(search && { search }),
        ...(metaData.order && { sort: `${metaData.sort === 'DESC' ? '-' : ''}${metaData.order}` }),
        report_type: type,
        report_version: 2,
        time_period: tempPeriod,
      };

      await reportApi.getPromoRequestReportNonDatamart(payload);

      setDialogInfoExport({
        isDialog: true,
        state: {
          title: (
            <Trans
              t={t}
              i18nKey="modal.exportRequest.label"
              defaults="Ekspor Laporan Promo"
            />
          ),
          description: (
            <Trans
              t={t}
              i18nKey="modal.exportRequest.description"
              defaults="Mohon menunggu, sistem sedang memproses <strong>Detail Penjualan</strong>. Anda akan menerima pemberitahuan notifikasi dan email saat data siap untuk diunduh."
              components={{ bold: <b /> }}
            />
          ),
          btnCloseOnly: false,
        },
      });
    } catch (error) {
      if (error.cause && error.cause.status && error.cause.status.code) {
        if (Number(error.cause.status.code) === 42200001) {
          setDialogInfoExport({
            isDialog: true,
            state: {
              title: (
                <Trans
                  t={t}
                  i18nKey="label.exportReport"
                  defaults="Ekspor Laporan"
                />
              ),
              description: error.cause.status.message,
              btnCloseOnly: true,
            },
          });
          return;
        }
      }

      addNotification({
        title: t('translation:toast.error', { defaultValue: 'Gagal!' }),
        message: (
          <span>
            <Trans
              t={t}
              i18nKey="Report/Sales/promo:modal.toast.failedExport"
              defaultValue="<bold>Laporan Promo</bold> gagal diekspor"
              components={{ bold: <b /> }}
            />
          </span>
        ),
        level: 'error',
      });
    } finally {
      hideProgress();
    }
  };

  const resetFilter = () => {
    resetDateRange();
    setTempCal([moment().startOf('month').toDate(), moment().endOf('month').toDate()]);
    setTempPeriod('day');
  };

  const setFilter = () => {
    changeDate(tempCal);
    setPeriod(tempPeriod);
  };

  return (
    <Paper
      responsive
      css={{
        padding: 0,
        mb: '$spacing-05',
        '@md': {
          padding: 20,
        },
      }}
    >
      <Flex css={{ flexDirection: 'column', rowGap: '$compact' }}>
        <TitleSection
          title={t('Report/Sales/promo:promoReport', { defaultValue: 'Laporan Promo' })}
          date={date}
          onDownloadLaporan={downloadLaporan}
          hideComparison
          t={t}
          isPrimary
        />
        <Separator
          css={{
            '@sm': { display: 'none' },
            '@md': { display: 'block' },
          }}
        />
        <BannerText css={{ my: 0 }} />
        <Box
          css={{
            '@sm': { display: 'none' },
            '@md': {
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              gap: 6,
            },
          }}
        >
          <Box
            css={{
              gap: '$compact',
              display: 'flex',
              '@md': { padding: '0px $spacing-03' },
            }}
          >
            <Box css={{ display: 'none', '@md': { display: 'block' } }}>
              <InputDatePicker
                type={ADVANCE_PICKER}
                onChange={changeDate}
                date={date}
                css={{ width: 258 }}
              />
            </Box>
            <Box
              css={{
                width: 128,
                '& > div > div': { borderColor: '$gray300' },
                '@sm': { display: 'none' },
                '@md': { display: 'block' },
              }}
            >
              <InputSelect
                size="sm"
                option={optFilter.map(group => ({
                  ...group,
                  isDisabled: group.value === 'hour' && !moment(date[0]).isSame(moment(date[1])),
                }))}
                placeholder="Pilih Kategori"
                value={optFilter.find(f => f.value === tempPeriod)}
                onChange={(value) => {
                  const selectedPeriod = optFilter.find(f => f.value === value.value).value;
                  setTempPeriod(selectedPeriod);
                  setPeriod(selectedPeriod);
                }
                }
              />
            </Box>
          </Box>
          <SalesIncreasement
            value={increasementNominal}
            promoPageHandler={goToOverviewSection}
            isTablet={isTablet}
            t={t}
          />
        </Box>
        <Box css={{ display: 'grid', gap: '$compact', '@md': { display: 'none' } }}>
          <Flex css={{ width: '100%', gap: '$compact', '@md': { gap: 'unset' } }}>
            <Button
              size="sm"
              buttonType="secondary"
              leftIcon={<FilterOutline color="currentColor" />}
              onClick={() => setOpenFilter(true)}
              css={{
                minWidth: 'unset',
                display: 'flex',
                alignItems: 'center',
                '& > div': { height: '24px' },
                '@md': { display: 'none' },
              }}
            >
              Filter
            </Button>
            <ExportReport
              onExport={downloadLaporan}
              calendar={{ start: date[0], end: date[1] }}
              title={t('Report/Sales/promo:promoReport', { defaultValue: 'Laporan Promo' })}
              type="multi"
              css={{
                display: 'inline-flex',
                width: '100%',
                '@md': {
                  display: 'none',
                },
              }}
            />
          </Flex>
          <Separator css={{ my: 4 }} />
          <SalesIncreasement
            value={increasementNominal}
            promoPageHandler={goToOverviewSection}
            isTablet={isTablet}
            t={t}
          />
        </Box>
        <Separator css={{ my: 4, '@sm': { display: 'none' }, '@md': { my: 0, display: 'block' } }} />
        <LineChart
          // data={[]}
          // legendData={[]}
          data={
            chartData && chartData.filteredChart ? chartData.filteredChart : []
          }
          legendData={
            chartData && chartData.chartLegend ? chartData.chartLegend : []
          }
          period={period}
          xAxisKey="date"
          title={t('Report/Sales/promo:promoChart', { defaultValue: 'Grafik Promo' })}
          onComparatorChange={x => handleComparator(x)}
          comparatorList={chartData.dataCompare}
          customXAxisFormat={d => handleXaxis(d)}
        />
        <div ref={overviewRef} style={{ display: 'none' }} />
        <Separator
          css={{
            my: 4,
            '@md': {
              margin: '$spacing-03 -16px $spacing-06 -16px',
            },
          }}
          style={{ width: 'auto' }}
        />
        <TitleSection
          title={t('Report/Sales/promo:promoReport', { defaultValue: 'Laporan Promo' })}
          date={date}
          hideDownloadButton
          hideComparison
          hideTooltipGuidance
        />
        <Separator
          css={{
            '@sm': { display: 'none' },
            '@md': { display: 'block' },
          }}
        />
        <Box
          css={{
            display: 'block',
            '@sm': {
              padding: 0,
              margin: 0,
            },
            '@md': {
              display: 'none',
              width: 320,
              padding: '0px',
            },
          }}
        >
          <InputSearchbox
            placeholder={t('translation:placeholder.search', { defaultValue: 'Cari ...' })}
            onChange={debounce((val) => {
              setSearch(val);
            }, 700)}
          />
        </Box>
        <Box
          css={{
            display: 'none',
            '@sm': {
              padding: 0,
              margin: 0,
            },
            '@md': {
              display: 'block',
              width: 320,
              padding: '0px',
            },
          }}
        >
          <InputSearchbox
            placeholder={t('translation:placeholder.search', { defaultValue: 'Cari ...' })}
            onChange={debounce((val) => {
              setSearch(val);
            }, 700)}
          />
        </Box>
        <CardWrapper
          tooltipCSS={{
            width: 315,
          }}
          tooltipPositionDynamic={
            [
              {
                side: 'top',
                align: 'center',
              },
              null,
              {
                side: 'top',
                align: 'end',
              },
            ]
          }
          data={[
            {
              color: 'green',
              label: t('Report/Sales/promo:summary.totalTransaction.label', { defaultValue: 'Total Transaksi dengan Promo' }),
              tooltip: t('Report/Sales/promo:summary.totalTransaction.tooltip', { defaultValue: 'Hanya menampilkan total transaksi dari seluruh transaksi yang memiliki dan menggunakan promo' }),
              description: (summaryData && summaryData.transaction_count && numSeparator(summaryData.transaction_count)) || 0,
            },
            {
              color: 'blue',
              label: t('Report/Sales/promo:summary.promoValue', { defaultValue: 'Nilai Promo' }),
              description: (summaryData && summaryData.promo_total && formatCurrency(summaryData.promo_total)) || 0,
            },
            {
              color: 'purple',
              label: t('Report/Sales/promo:summary.totalSales.label', { defaultValue: 'Total Penjualan dengan Promo' }),
              tooltip: t('Report/Sales/promo:summary.totalSales.tooltip', { defaultValue: 'Hanya menampilkan total penjualan dari seluruh transaksi yang memiliki dan menggunakan promo' }),
              description: (summaryData && summaryData.transaction_total && formatCurrency(summaryData.transaction_total)) || 0,
            },
          ]}
        />
        <Separator
          css={{
            margin: '$spacing-05 0 0 0',
            '@sm': { display: 'none' },
            '@md': { display: 'block' },
          }}
        />
        <Table
          id="report_promo_promo"
          data={tableData}
          columns={tableColumns(handleDetail, t)}
          totalData={(metaData && metaData.total) || 0}
          keyId="-"
          isLoading={isLoading}
          pageIndex={metaData.page}
          fetchData={(data) => {
            if (data.sortAccessor && data.sortDirection) {
              setMetaData({
                ...metaData,
                page: data.pageIndex,
                limit: data.pageSize,
                sort: data.sortDirection,
                order: data.sortAccessor,
              });
            } else {
              setMetaData({
                ...metaData,
                page: data.pageIndex,
                limit: data.pageSize,
              });
            }
          }}
          onRowClick={({ original }) => handleDetail({
            value: original.key,
            row: {
              original,
            },
          })
          }
          searchQuery={search}
          css={{ padding: 0 }}
        />
      </Flex>

      <AlertDialog
        isMobile={isMobile}
        onConfirm={() => {
          setDialogInfoExport(prev => ({ ...prev, isDialog: false }));
          addNotification({
            title: t('toast.success', { ns: 'translation', defaultValue: 'Berhasil!' }),
            message: (
              <Trans
                t={t}
                i18nKey="modal.toast.successExportRequest"
                components={{ bold: <b /> }}
              >
                Laporan
                {' '}
                <strong>Promo</strong>
                {' '}
                dalam proses ekspor
              </Trans>
            ),
            level: 'success',
          });
        }}
        onCancel={() => setDialogInfoExport(prev => ({ ...prev, isDialog: false }))}
        open={dialogInfoExport.isDialog}
        title={dialogInfoExport.state.title}
        description={dialogInfoExport.state.description}
        labelConfirm={(
          <Trans
            t={t}
            i18nKey="label.confirm"
            defaults="Oke, Mengerti"
            ns="translation"
          />
        )}
        labelCancel={(
          <Trans
            t={t}
            i18nKey="label.close"
            defaults="Tutup"
            ns="translation"
          />
        )}
        css={{
          width: isMobile ? 'unset' : '422px',
        }}
        actionButtonProps={{ size: 'md' }}
        cancelButtonProps={{ size: 'md' }}
        singleButton={!dialogInfoExport.state.btnCloseOnly}
        hideActionButton={dialogInfoExport.state.btnCloseOnly}
      />

      <PageDialog open={openFilter} onOpenChange={setOpenFilter}>
        <PageDialog.Title>
          Filter
        </PageDialog.Title>
        <PageDialog.Content
          css={{
            display: 'flex', flexDirection: 'column', gap: '$compact', backgroundColor: '$white', padding: '$spacing-05',
          }}
        >
          <FormGroup>
            <FormLabel css={{ color: '$textPrimary' }}>{t('translation:label.selectDate', { defaultValue: 'Pilih Tanggal' })}</FormLabel>
            <InputDatePicker
              size="lg"
              type="range"
              onChange={(d) => { setTempCal(d); resetDateRange(); }}
              date={tempCal}
            />
          </FormGroup>
          <FormGroup>
            <FormLabel css={{ color: '$textPrimary' }}>{t('translation:label.dateRange', { defaultValue: 'Rentang Tanggal' })}</FormLabel>
            <InputDateRange
              key={dateRangeKey}
              size="lg"
              onChange={setTempCal}
            />
          </FormGroup>
          <FormGroup>
            <FormLabel css={{ color: '$textPrimary' }}>{t('translation:label.period', { defaultValue: 'Periode' })}</FormLabel>
            <InputSelect
              size="lg"
              option={optFilter || []}
              placeholder="Pilih Kategori"
              value={optFilter.find(f => f.value === tempPeriod)}
              onChange={value => setTempPeriod(
                optFilter.find(f => f.value === value.value).value,
              )}
            />
          </FormGroup>
        </PageDialog.Content>
        <PageDialog.Footer css={{ gap: '$compact' }}>
          <Button size="md" buttonType="ghost" css={{ flex: '1' }} onClick={() => resetFilter()}>
            Reset
          </Button>
          <DialogClose asChild>
            <Button size="md" css={{ flex: 1 }} onClick={() => setFilter()}>
              {t('translation:label.apply', { defaultValue: 'Terapkan' })}
            </Button>
          </DialogClose>
        </PageDialog.Footer>
      </PageDialog>
    </Paper>
  );
}

const mapStateToProps = state => ({
  isFetching: state.report.isFetching,
  logo: state.user.profile.user_usaha_logo_path,
  listCabang: state.branch.list,
  namaUser: state.user.profile.user_name,
  namaUsaha: state.user.profile.user_usaha_name,
  selectedCabang: state.branch.filter,
  supportNeeded: state.layout.supportNeeded,
});

ReportPromo.propTypes = {
  calendar: PropTypes.shape({
    start: PropTypes.string,
    end: PropTypes.string,
  }).isRequired,
  assignCalendar: PropTypes.func.isRequired,
  router: PropTypes.shape({
    push: PropTypes.func,
  }).isRequired,
  hideProgress: PropTypes.func.isRequired,
  showProgress: PropTypes.func.isRequired,
  filterBranch: PropTypes.oneOf([PropTypes.string, PropTypes.number]).isRequired,
  addNotification: PropTypes.func.isRequired,
};

export default connect(mapStateToProps)(CoreHOC(ReportPromo));

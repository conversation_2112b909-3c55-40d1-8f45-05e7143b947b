import React, { useContext } from 'react';
import {
    Button, Flex, Grid, Paragraph, TagStatus,
    GridItem, Paper,
    Separator, IconButton,
} from '@majoo-ui/react';
import { CopyOutline } from '@majoo-ui/icons';
import SatuSehatContext from '../context/SatuSehatContext';
import { colors, styled } from '../../../../stitches.config';
import TitleSection from './TitleSection';
import SetupModal from './SetupModal';

const Container = styled(Flex, {
    marginTop: '$spacing-05',
    padding: '$spacing-05',
    flexDirection: 'column',
    gap: '$spacing-05',
    alignItems: 'start',
    border: '1px solid #EEF0F0',
    borderRadius: '8px',
});

function MainSection() {
    const {
        isMobile,
        t,
        setModalState,
        modalState: { setupModalOpen },
        integrationDetail: {
            orgId,
            clientKey,
            clientSecret,
            hasIntegrated,
        },
    } = useContext(SatuSehatContext);
    return (
        <React.Fragment>
            <Paper
                responsive
                css={{
                    padding: 'unset',
                    '@lg': {
                        padding: '20px $spacing-06',
                    },
                }}
            >
                <TitleSection />
                <Separator />
                <Container>
                    <Grid columns={isMobile ? 2 : 3} gap={2} css={{ marginTop: '$spacing-03', width: '100%' }}>
                        <GridItem css={{ minWidth: 0 }}>
                            <Flex direction="column">
                                <Paragraph paragraph="longContentRegular" color="primary">{t('mainSection.form.integrationStatus', 'Status Integrasi')}</Paragraph>
                                <TagStatus
                                    size="lg"
                                    type={hasIntegrated ? 'success' : 'default'}
                                    css={{
                                        maxWidth: 'unset',
                                        width: 'fit-content',
                                    }}
                                >
                                    {t(`mainSection.${hasIntegrated ? 'integrated' : 'notIntegrated'}`, hasIntegrated ? 'Terintegrasi' : 'Belum Terintegrasi')}
                                </TagStatus>
                            </Flex>
                        </GridItem>
                        {!isMobile ? (
                            <GridItem colSpan={2} css={{ justifySelf: 'right' }}>
                                <Button
                                    buttonType="secondary"
                                    size="sm"
                                    onClick={() => setModalState({ setupModalOpen: true })}
                                >
                                    {t(`mainSection.${orgId ? 'update' : 'add'}`, orgId ? 'Ubah Data' : 'Integrasi Satu Sehat')}
                                </Button>
                            </GridItem>
                        ) : null}
                        <GridItem css={{ minWidth: 0 }}>
                            <Flex direction="column" gap={3}>
                                <Paragraph paragraph="longContentRegular" color="primary">
                                    Organization ID
                                </Paragraph>
                                <Flex align="center" gap={3}>
                                    <Paragraph color="primary" isTruncated>
                                        {orgId || '-'}
                                    </Paragraph>
                                    {hasIntegrated ? (
                                        <IconButton onClick={() => { navigator.clipboard.writeText(orgId); }}>
                                            <CopyOutline color={colors.iconGreen} />
                                        </IconButton>
                                    ) : null}
                                </Flex>
                            </Flex>
                        </GridItem>
                        <GridItem css={{ minWidth: 0 }}>
                            <Flex direction="column" gap={3}>
                                <Paragraph paragraph="longContentRegular" color="primary">
                                    Client Key
                                </Paragraph>
                                <Flex align="center" gap={3}>
                                    <Paragraph color="primary" isTruncated>
                                        {clientKey || '-'}
                                    </Paragraph>
                                    {hasIntegrated ? (
                                        <IconButton onClick={() => { navigator.clipboard.writeText(clientKey); }}>
                                            <CopyOutline color={colors.iconGreen} />
                                        </IconButton>
                                    ) : null}
                                </Flex>
                            </Flex>
                        </GridItem>
                        <GridItem css={{ minWidth: 0 }}>
                            <Flex direction="column" gap={3}>
                                <Paragraph paragraph="longContentRegular" color="primary">
                                    Secret Key
                                </Paragraph>
                                <Flex align="center" gap={3}>
                                    <Paragraph color="primary" isTruncated>
                                        {clientSecret || '-'}
                                    </Paragraph>
                                    {hasIntegrated ? (
                                        <IconButton onClick={() => { navigator.clipboard.writeText(clientSecret); }}>
                                            <CopyOutline color={colors.iconGreen} />
                                        </IconButton>
                                    ) : null}
                                </Flex>
                            </Flex>
                        </GridItem>
                    </Grid>
                    {isMobile ? (
                        <Button
                            buttonType="secondary"
                            size="sm"
                            onClick={() => setModalState({ setupModalOpen: true })}
                            css={{ width: '100%' }}
                        >
                            {t(`mainSection.${orgId ? 'update' : 'add'}`, orgId ? 'Ubah Data' : 'Integrasi Satu Sehat')}
                        </Button>
                    ) : null}
                </Container>
            </Paper>
            {setupModalOpen ? (
                <SetupModal />
            ) : null}
        </React.Fragment>
    );
}

export default MainSection;

export const sidebarIconMap = (icon, isMobile = false, name) => {
    let iconName = '';
    switch (icon) {
        case 'icon-dashboard':
            iconName = 'Dashboard';
            break;
        case 'icon-refund':
            iconName = 'Refund';
            break;
        case 'icon-report':
            iconName = 'Report';
            break;
        case 'icon-analisa':
            iconName = 'ReportAnalysis';
            break;
        case 'icon-item':
            iconName = 'Product';
            break;
        case 'icon-inventory':
            iconName = 'Inventories';
            break;
        case 'icon-customer':
            iconName = 'Customer';
            break;
        case 'icon-promotion':
            iconName = 'Promotion';
            break;
        case 'icon-hand':
            iconName = 'Commission';
            break;
        case 'icon-invoice':
            iconName = 'Invoice';
            break;
        case 'icon-marketing':
            iconName = 'Marketing';
            break;
        case 'icon-buku':
            iconName = 'CashBook';
            break;
        case 'icon-pemasukan':
            iconName = 'Reception';
            break;
        case 'icon-pengeluaran':
            iconName = 'Expense';
            break;
        case 'icon-receipt':
            iconName = 'FinanceReport';
            break;
        case 'icon-daftar-akun':
            iconName = 'AccountList';
            break;
        case 'icon-mail':
            iconName = 'Inbox';
            break;
        case 'icon-bell':
            iconName = 'Notification';
            break;
        case 'icon-info-bisnis':
            iconName = 'Outlet';
            break;
        case 'icon-money':
            iconName = 'Payment';
            break;
        case 'icon-karyawan':
            iconName = 'Employee';
            break;
        case 'icon-terminal':
            iconName = 'Terminal';
            break;
        case 'icon-cashier':
            iconName = 'Cashier';
            break;
        case 'icon-user-outline':
            iconName = 'ProfileAccount';
            break;
        case 'icon-support':
            iconName = 'AccessSupport';
            break;
        case 'icon-digital-payment':
            iconName = 'DigitalPayment';
            break;
        case 'icon-webstore':
            iconName = 'Webstore';
            break;
        case 'icon-marketplace':
            iconName = 'Marketplace';
            break;
        case 'icon-order-online':
            iconName = 'OrderOnline';
            break;
        case 'icon-paid-service':
            iconName = 'PaidService';
            break;
        case 'icon-pengembangan':
            iconName = 'BusinessDevelopment';
            break;
        case 'icon-help':
            iconName = 'UsageGuide';
            break;
        case 'icon-reseller':
            iconName = 'MemberGetMember';
            break;
        case 'icon-group-inspirasi':
            iconName = 'Entrepreneur';
            break;
        case 'icon-discount':
            iconName = 'NewsPromo';
            break;
        case 'icon-calendar':
            iconName = 'Event';
            break;
        case 'icon-book':
            iconName = 'Majoopreneur';
            break;
        case 'icon-food-order':
            iconName = 'FoodOrder';
            break;
        case 'icon-majoo-teams':
            iconName = 'MajooTeams';
            break;
        case 'icon-revenue':
            iconName = 'Revenue';
            break;
        case 'icon-consumer-app':
            iconName = 'ConsumerApp';
            break;
        case 'icon-supplies':
            iconName = 'Belanja';
            break;
        case 'icon-favorite':
            iconName = 'Star';
            break;
        case 'icon-print':
            iconName = 'Print';
            break;
        case 'icon-import':
            iconName = 'Import';
            break;
        case 'icon-satusehat':
            iconName = 'SatuSehat';
            break;
        case 'icon-appointment':
            iconName = 'Calendar';
            break;
        case 'icon-reservation':
            iconName = 'Reservation';
            break;
        case 'icon-notification-list':
            iconName = 'NotificationList';
            break;
        default:
            iconName = 'Dashboard';
            break;
    }
    if (name) {
        if (name.toLowerCase() === 'kasir') iconName = 'CashierMachine';
        if (
            name.toLowerCase() === 'webstore' ||
            name.toLowerCase() === 'web store new' ||
            name.toLowerCase() === 'toko online'
        )
            iconName = 'Webstore';
        if (name.toLowerCase() === 'marketplace') iconName = 'Marketplace';
        if (name.toLowerCase() === 'layanan berbayar') iconName = 'PaidService';
        if (name.toLowerCase() === 'gaji') iconName = 'SettingPaymentMethod';
        if (name.toLowerCase() === 'food order') iconName = 'FoodOrder';
        if (name.toLowerCase() === 'pesanan' || name.toLowerCase() === 'daftar pesanan') iconName = 'OrderOnline';
    }
    return `${iconName}${isMobile ? 'TwoTone' : 'Outline'}`;
};

const setProperty = (obj, path, value) => {
    const [head, ...rest] = path.split('.');

    return {
        ...obj,
        [head]: rest.length ? setProperty(obj[head], rest.join('.'), value) : value,
    };
};

/**
 * Still needs improvements
 */
// export const addAdditionalSidebarMenu = (additionalMenu, activeGroupMenu, sidebarMenus) => {
//     const modifiedMenus = [...sidebarMenus];
//     if (Number(additionalMenu.groupMenuId) === Number(activeGroupMenu)) {
//         const parentMenu = sidebarMenus[additionalMenu.parentMenuIndex];
//         const parentMenuDetail = [...parentMenu.detail];
//         parentMenuDetail.splice(additionalMenu.menuIndex, 0, additionalMenu.menu);
//         const modifiedMenu = {
//             ...parentMenu,
//             detail: parentMenuDetail,
//         };
//         modifiedMenus[additionalMenu.parentMenuIndex] = modifiedMenu;
//         return modifiedMenus;
//     }
//     return sidebarMenus;
// };

/**
 * # HOW TO USE
 * parentMenuIds[] => id of parentMenuId and index of array representative of level [0, 1, 2, ..., n-1, n]
 * positionIndex => index of menu to inserted
 * additionalMenus => menus will be inserted
 */
// const parentsAndAdditionalMenus = [
//     {
//         parentMenuIds: [],
//         positionIndex: 0,
//         additionalMenus: [],
//     },
//     {
//         parentMenuIds: [],
//         positionIndex: 0,
//         additionalMenus: [],
//     },
// ]

export const addAdditionalSidebarMenu = ({ parentsAndAdditionalMenus, sidebarMenus, activeMenuGroup }) => {
    let modifiedMenus = [...sidebarMenus];

    parentsAndAdditionalMenus.forEach(({ parentMenuIds, positionIndex, additionalMenu, rootMenuIds }) => {
        if (rootMenuIds.includes(activeMenuGroup)) {
            let newMenus = [...modifiedMenus];
            let firstMenuIndex = -1;
            const newMenuIndexes = [];

            parentMenuIds.forEach((parentMenuId, index) => {
                const parentMenuIndex = newMenus.findIndex(menu => Number(menu.id) === Number(parentMenuId));
                if (parentMenuIndex > -1) {
                    newMenus = [...newMenus[parentMenuIndex].detail];
                    if (index === 0) {
                        firstMenuIndex = parentMenuIndex;
                    } else {
                        newMenuIndexes.push(parentMenuIndex);
                    }
                }
            });

            newMenus.splice(positionIndex, 0, additionalMenu);

            if (firstMenuIndex > -1) {
                let path = '';
                newMenuIndexes.forEach(menuIndex => {
                    path += `detail.${menuIndex}.`;
                });
                path += 'detail';

                modifiedMenus[firstMenuIndex] = setProperty(modifiedMenus[firstMenuIndex], path, newMenus);
            } else {
                modifiedMenus = newMenus;
            }
        }
    });

    return modifiedMenus;
};

/**
 * Still needs improvements
 */
// export const removeSidebarMenu = (parentMenuId, removedMenuId, activeGroupMenu, sidebarMenus) => {
//     const modifiedMenus = [...sidebarMenus];
//     if (Number(parentMenuId) === Number(activeGroupMenu)) {
//         const removedMenuIndex = sidebarMenus.indexOf(sidebarMenus.find(menu => menu.id === removedMenuId));
//         if (removedMenuIndex > -1) {
//             modifiedMenus.splice(removedMenuIndex, 1);
//         }
//         return modifiedMenus;
//     }
//     return sidebarMenus;
// };

/**
 * # HOW TO USE
 * parentMenuIds[] => id of parentMenuId and index of array representative of level [0, 1, 2, ..., n-1, n]
 * removedMenuId => id of menu to removed
 */
// const parentsAndRemovedMenus = [
//     {
//         parentMenuIds: [],
//         removedMenuId: '',
//     },
//     {
//         parentMenuIds: [],
//         removedMenuId: '',
//     },
// ]

export const removeSidebarMenu = ({ parentsAndRemovedMenus, sidebarMenus }) => {
    let modifiedMenus = [...sidebarMenus];

    parentsAndRemovedMenus.forEach(({ parentMenuIds, removedMenuId }) => {
        let newMenus = [...modifiedMenus];
        let firstMenuIndex = -1;
        const newMenuIndexes = [];

        parentMenuIds.forEach((parentMenuId, index) => {
            const parentMenuIndex = newMenus.findIndex(menu => Number(menu.id) === Number(parentMenuId));
            if (parentMenuIndex > -1) {
                newMenus = newMenus[parentMenuIndex].detail;
                if (index === 0) {
                    firstMenuIndex = parentMenuIndex;
                } else {
                    newMenuIndexes.push(parentMenuIndex);
                }
            }
        });

        const removeMenuIndex = newMenus.findIndex(menu => Number(menu.id) === Number(removedMenuId));
        if (removeMenuIndex > -1) {
            newMenus = newMenus.filter((_, index) => index !== removeMenuIndex);
        }

        if (firstMenuIndex > -1) {
            let path = '';
            newMenuIndexes.forEach(menuIndex => {
                path += `detail.${menuIndex}.`;
            });
            path += 'detail';

            modifiedMenus[firstMenuIndex] = setProperty(modifiedMenus[firstMenuIndex], path, newMenus);
        } else {
            modifiedMenus = newMenus;
        }
    });

    return modifiedMenus;
};

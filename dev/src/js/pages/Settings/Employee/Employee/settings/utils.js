import { styled } from '@stitches/react';
import { Box } from '@majoo-ui/react';
import XLSX from 'xlsx/dist/xlsx.core.min';
import { fileObjectToBinary } from '~/utils/helper';
import { isEqual } from 'lodash';

export const LabelWrapper = styled(Box, {
    width: '20%',
    marginTop: '10px',
    '@sm': {
        width: '100%',
        marginTop: 0,
    },
    variants: {
        fullWidth: {
            true: {
                width: '100%',
                marginTop: '0px',
                marginBottom: '10px',
            },
        },
    },
    '& .form-label': {
        alignItems: 'start !important',
        '& span': {
            color: '$textPrimary',
            fontSize: '$section-sub-title',
            fontWeight: 600,
            '@sm': { fontSize: '$label', fontWeight: 500 },
            lineHeight: '$section-sub-title',
            letterSpacings: '$section-sub-title',
            '& > h3': {
                '@sm': { paddingBottom: 8 },
                '@md': { paddingBottom: 0 },
            },
        },
    },
});

export const ContentWrapper = styled(Box, {
    width: '80%',
    '@sm': {
        width: '100%',
    },
});

const fileImportType = [
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/wps-office.xlsx',
];

const convertSpreadSheetToObject = async (file, range, t) => {
    const convertFileToBinary = await fileObjectToBinary(file);
    const workbook = XLSX.read(convertFileToBinary, { type: 'binary' });

    const workbookSheetTemplate = workbook.Sheets['Template Impor Data Karyawan'];
    const workbookSheetTemplateFailed = workbook.Sheets['Data Karyawan Gagal'];

    const workbookRowsData = XLSX.utils.sheet_to_json(
        workbookSheetTemplate || workbookSheetTemplateFailed,
        { ...range },
    );
    return workbookRowsData;
};

const validationArrayRule = t => t('importExport.columnImport', {
    returnObjects: true,
    defaultValue: [
        "Nama Karyawan*",
        "NIP*",
        "Akses Karyawan*(Y/T)",
        "Telepon*",
        "Email*",
        "Hak Akses*",
        "Posisi",
        "Outlet*",
        "Tempat Lahir",
        "Tanggal Lahir(DD/MM/YYYY)",
        "Jenis Kelamin",
        "Agama",
        "Status Perkawinan",
        "Nomor KTP",
        "Email Pribadi",
        "No Ponsel",
        "Alamat Sesuai KTP",
        "Alamat Domisili",
        "Perusahaan Sebelumnya",
        "Posisi Sebelumnya",
        "Tanggal Mulai(DD/MM/YYYY)",
        "Tanggal Selesai(DD/MM/YYYY)",
        "Perusahaan Sebelumnya(II)",
        "Posisi Sebelumnya(II)",
        "Tanggal Mulai(II)",
        "Tanggal Selesai(II)",
        "Gelar Pendidikan",
        "Jurusan Pendidikan",
        "Lembaga",
        "IPK",
        "Nomor NPWP",
        "Nomor BPJSTK",
        "Nomor BPJS Kesehatan",
        "Nama Kontak Darurat",
        "No Ponsel Kontak Darurat",
        "Hubungan",
        "Nama Tanggungan",
        "No Ponsel Tanggungan",
        "Hubungan",
        "Nama Bank",
        "Nomor Rekening",
        "Nama Pemilik Rekening",
        "Posisi - Tingkat",
        "Tipe Karyawan",
        "Tanggal Bergabung(DD/MM/YYYY)",
        "Tanggal Awal Kontrak(DD/MM/YYYY)",
        "Tanggal Selesai Kontrak(DD/MM/YYYY)"
    ],
});

const validationImportedFileStructure = (workbookStringVal, t) => {
    const parseStringDataImport = workbookStringVal.reduce((temp, item, index) => {
        if (index === 0) Object.keys(item).map(itemKey => temp.push(item[itemKey].replace(/\r/g, '').replace(/\n/g, '')));
        return temp;
    }, []);

    return isEqual(validationArrayRule(t), parseStringDataImport);
};

export const importFileValidation = async ({
    file, range, t,
}) => {
    try {
        // Filetype Validation
        const { type, name } = file;
        if (fileImportType.indexOf(type) === -1) throw Error(t('translation:error.formatUnsupported', 'Format berkas tidak didukung, hanya mendukung format .xls'));

        const convertFileToObject = await convertSpreadSheetToObject(file, range, t);
        const convertFileToObjectData = await convertSpreadSheetToObject(file, undefined, t);
        // Template Validation
        if (!validationImportedFileStructure(convertFileToObject, t)) throw Error(t('translation:toast.errorTemplate', 'Template tidak sesuai, gunakan template yang disediakan'));
        if (!convertFileToObjectData.length) {
            throw Error(t('translation:toast.errorTemplate', 'Template tidak sesuai, gunakan template yang disediakan'));
        }

        return {
            fileOutput: {
                name,
                file,
            },
            validationResult: true,
            errorMessage: '',
        };
    } catch (error) {
        return {
            file: null,
            validationResult: false,
            errorMessage: error.message || t('translation:toast.error', 'Gagal!'),
        };
    }
};
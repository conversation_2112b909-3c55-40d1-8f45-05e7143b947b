import i18n from 'i18next';
import { AlertDialogFunction } from '@majoo-ui/react';
import moment from 'moment';
import { getPractitionerList } from '~/data/satuSehat';
import { uploadImage as uploadImageAPI } from '~/data/upload';
import { getEmployeeTypeList } from "~/data/settings";
import {
    changePinEmployee,
    createEmployeeV2,
    getEmployeeNIK,
    getEmployeeV2List,
    resendVerificationCode,
    updateEmployeeV2,
    verifikasiSupervisor,
    sendUserAccess,
    getEmployeeV2,
    getEmployeeHistory,
    updateEmployeeHistory,
    getEmployeeSubs,
    transferEmployee,
    terminateEmployeeV2,
    deleteEmployeeV2 as apiDeleteEmployeeV2,
    exportEmployeeTemplate,
    importEmployeeData,
    exportEmployee,
    checkExportEmployee,
    importEmployeeDocument,
} from '../../../../../data/employee';
import { getallNegaraAndWilayah } from '../../../../../data/locations';
import { getBank } from '../../../../../data/outlets';
import { getOutlet } from '../../../../../data/outlets/api';
import { getRoles } from '../../../../../data/role';
import { catchError } from '../../../../../utils/helper';
import { FIRST_STEP, jobStatusEnum, TRANSLATION_NAMESPACES } from '../settings/constants';
// import config from '../../../../../utils/user.util';

const { t } = i18n;

export const fetchEmployee = async (stateTable = {}, contextData, onSuccessfully) => {
    const {
        filterBranch, searchKeyword, filterStatus, showProgress, hideProgress, addToast,
    } = contextData;

    const {
        pageSize = 10, pageIndex = 0, sortDirection, sortAccessor,
    } = stateTable;

    const payload = {
        limit: pageSize,
        page: pageIndex + 1,
        ...(sortAccessor && sortDirection) && { order: sortDirection.toUpperCase(), sort: sortAccessor },
        ...(searchKeyword !== '') && { search: searchKeyword },
        ...(filterStatus !== '') && { is_terminated: filterStatus },
        ...(!!filterBranch) && { outlet_id: Number(filterBranch) },
    };

    try {
        showProgress();
        const { data, meta: { total, page: currentPage } } = await getEmployeeV2List(payload);

        onSuccessfully({
            tableData: data || [],
            totalData: total,
            currentPage: currentPage - 1,
        });
    } catch (e) {
        addToast({
            title: t('toast.error', { ns: TRANSLATION_NAMESPACES.GLOBAL }),
            description: catchError(e),
            variant: 'failed',
        });
    } finally {
        hideProgress();
    }
};

export const fetchEmployeeNIK = async (contextData, onSuccessfully) => {
    const {
        showProgress, hideProgress, addToast,
    } = contextData;

    try {
        showProgress();
        const { code, status } = await getEmployeeNIK();
        if (status) onSuccessfully(code);
    } catch (e) {
        addToast({
            title: t('toast.error', { ns: TRANSLATION_NAMESPACES.GLOBAL }),
            description: catchError(e),
            variant: 'failed',
        });
    } finally {
        hideProgress();
    }
};

export const fetchOutlet = async (contextData, onSuccessfully) => {
    const {
        showProgress, hideProgress, addToast,
    } = contextData;

    try {
        // showProgress();
        const { data, status } = await getOutlet({ is_cms: 1 });
        if (!status) throw new Error('error mendapatkan outlet');
        onSuccessfully(data);
    } catch (e) {
        addToast({
            title: t('toast.error', { ns: TRANSLATION_NAMESPACES.GLOBAL }),
            description: catchError(e),
            variant: 'failed',
        });
    } finally {
        // hideProgress();
    }
};

export const fetchRole = async (contextData, onSuccessfully) => {
    const {
        showProgress, hideProgress, addToast,
    } = contextData;

    try {
        // showProgress();
        const { data } = await getRoles({ page: -1 });
        onSuccessfully(data);
    } catch (e) {
        addToast({
            title: t('toast.error', { ns: TRANSLATION_NAMESPACES.GLOBAL }),
            description: catchError(e),
            variant: 'failed',
        });
    } finally {
        // hideProgress();
    }
};

// use in FormModal, FormOverview
export const mappingDataEmployeeDetail = (detail) => {
    const {
        id, employee_photo_url: fotoEmployee, employee_name: nama, employee_number: nomorKaryawan,  is_verified: isVerified, active_status: employeeAccess, employee_phone: telepon, work_email: email, employee_position: posisi, permission_id: hakAkses, outlet_id: outlet, outlets: multiOutlet, pin,
        birth_place: tempatLahir, birth_date: tglLahir, gender, religion, marital_status: statusKawin, nik: ktp, personal_email: personalEmail, personal_phone: personalPhone, identity_address: alamatKTP, domisili_address: alamat, domisili_province_id: provinsi, domisili_city_id: kota,
        attachment_cv_url: cv, attachment_portfolio_url: portofolio, work_experiences: workingExperience, education_degree: educationalDegree, education_major: educationDepartment, education_institute: institution, education_ipk: gpa, tax_number: npwp, number_bpjs_health: bpjsKesehatan, number_bpjs_employment: bpjsKetenagakerjaan,
        document_ktp_url: fotoKTP, document_kk_url: fotoKK, document_npwp_url: fotoNPWP, document_paklaring_url: fotoPaklaring, document_bpjs_health_url: fotoBpjsKesehatan, document_bpjs_employment_url: fotoBpjsKetenagakerjaan, document_diploma_url: fotoIjasah, attachment_job_description_url: fotoDeskripsiPekerjaan, attachment_current_sk_url: fotoSK,
        emergency_contact_name: emergencyContactName, emergency_contact_phone: emergencyContactPhone, emergency_contact_relation: emergencyContactRelationship, dependent_name: liabilityName, dependent_phone: liabilityPhone, dependent_relation: liabilityRelationship, bank_id: bank, bank_account_number: noRekening, bank_account_name: pemilikRekening, saving_book_photo_url: fotoBukuTabungan,
        recruitment_location: jobLocationRecruitment, join_date: tglBergabung, termination_date: tglBerakhir, report_to: directReport,
        department_id: jobDepartement, jobHistory, subordinated, is_terminated: isTerminated, type,
        is_owner: isOwner, mayang_id: idEmployeeMayang,
    } = detail;

    const form = {
        // data utama
        step: FIRST_STEP,
        id,
        idEmployeeMayang,
        isOwner,
        fotoEmployee: fotoEmployee ? [{
            name: fotoEmployee.replace(/^.*[\\/]/, ''),
            url: fotoEmployee,
        }] : [],
        nama,
        nik: nomorKaryawan,
        isVerified,
        employeeAccess,
        telepon,
        email,
        emailPrevious: email,
        posisi,
        hakAkses,
        hakAksesPrevious: hakAkses,
        outlet,
        multiOutlet: multiOutlet ? multiOutlet.map(x => ({ value: String(x.outlet_id), is_default: x.is_default })) : [],
        pin,
        // personal
        tempatLahir,
        tglLahir: tglLahir ? moment(tglLahir, 'YYYY-MM-DD').toDate() : null,
        jenisKelamin: gender,
        agama: religion,
        statusKawin,
        ktp,
        personalEmail,
        personalPhone,
        alamatKTP,
        alamat,
        provinsi,
        kota,
        cv: cv ? [{
            name: cv.replace(/^.*[\\/]/, ''),
            url: cv,
        }] : [],
        portofolio: portofolio ? [{
            name: portofolio.replace(/^.*[\\/]/, ''),
            url: portofolio,
        }] : [],
        workingExperience: workingExperience.map(x => ({ companyName: x.company,position: x.position, startDate: x.start_date ? moment(x.start_date, 'YYYY-MM-DD').toDate() : null, endDate: x.end_date ? moment(x.end_date, 'YYYY-MM-DD').toDate() : null })),
        educationalDegree,
        educationDepartment,
        institution,
        gpa,
        npwp,
        bpjsKetenagakerjaan,
        bpjsKesehatan,
        fotoKTP: fotoKTP ? [{
            name: fotoKTP.replace(/^.*[\\/]/, ''),
            url: fotoKTP,
        }] : [],
        fotoKK: fotoKK ? [{
            name: fotoKK.replace(/^.*[\\/]/, ''),
            url: fotoKK,
        }] : [],
        fotoNPWP: fotoNPWP ? [{
            name: fotoNPWP.replace(/^.*[\\/]/, ''),
            url: fotoNPWP,
        }] : [],
        fotoPaklaring: fotoPaklaring ? [{
            name: fotoPaklaring.replace(/^.*[\\/]/, ''),
            url: fotoPaklaring,
        }] : [],
        fotoBpjsKesehatan: fotoBpjsKesehatan ? [{
            name: fotoBpjsKesehatan.replace(/^.*[\\/]/, ''),
            url: fotoBpjsKesehatan,
        }] : [],
        fotoBpjsKetenagakerjaan: fotoBpjsKetenagakerjaan ? [{
            name: fotoBpjsKetenagakerjaan.replace(/^.*[\\/]/, ''),
            url: fotoBpjsKetenagakerjaan,
        }] : [],
        fotoIjasah: fotoIjasah ? [{
            name: fotoIjasah.replace(/^.*[\\/]/, ''),
            url: fotoIjasah,
        }] : [],
        fotoDeskripsiPekerjaan: fotoDeskripsiPekerjaan ? [{
            name: fotoDeskripsiPekerjaan.replace(/^.*[\\/]/, ''),
            url: fotoDeskripsiPekerjaan,
        }] : [],
        fotoSK: fotoSK ? [{
            name: fotoSK.replace(/^.*[\\/]/, ''),
            url: fotoSK,
        }] : [],
        emergencyContactName,
        emergencyContactPhone,
        emergencyContactRelationship,
        liabilityName,
        liabilityPhone,
        liabilityRelationship,
        bank,
        noRekening,
        pemilikRekening,
        fotoBukuTabungan: fotoBukuTabungan ? [{
            name: fotoBukuTabungan.replace(/^.*[\\/]/, ''),
            url: fotoBukuTabungan,
        }] : [],
        // data karyawan
        jobDepartement,
        jobPosition: jobHistory.length > 0 ? jobHistory[0].position_id : '',
        jobLocationRecruitment,
        tglBergabung: moment(tglBergabung, 'YYYY-MM-DD').isValid() ? moment(tglBergabung, 'YYYY-MM-DD').toDate() : '',
        // employeeType: jobHistory.length > 0 ? jobHistory[0].employee_type : '',
        employeeType: type,
        firstDate: jobHistory.length > 0 && moment(jobHistory[0].start_date, 'YYYY-MM-DD').isValid() ? moment(jobHistory[0].start_date, 'YYYY-MM-DD').toDate() : '',
        tglBerakhir: moment(tglBerakhir, 'YYYY-MM-DD').isValid() ? moment(tglBerakhir, 'YYYY-MM-DD').toDate() : '',
        directReport,
        bawahan: subordinated ? subordinated.map(x => ({ id: x.id, name: x.employee_name, directReport: '' })) : [],
        isTerminated,
    };

    return form;
}

export const fetchEmployeeDetail = async (contextData, onSuccessfully, id) => {
    const {
        showProgress, hideProgress, addToast,
    } = contextData;

    try {
        showProgress();
        const { data: detail } = await getEmployeeV2(id);
        onSuccessfully({ id, detail });
    } catch (e) {
        addToast({
            title: t('toast.error', { ns: TRANSLATION_NAMESPACES.GLOBAL }),
            description: catchError(e),
            variant: 'failed',
        });
    } finally {
        hideProgress();
    }
};

export const createUserAccess = async ({ context, payload }, onSuccessfully) => {
    const {
        showProgress, hideProgress, addToast,
    } = context;

    const errorPhoneEmpty = 'phone_number is required';

    try {
        showProgress();
        const { code, message } = await sendUserAccess(payload);
        if (code === 200) onSuccessfully(message);
    } catch (e) {
        addToast({
            title: t('toast.error', { ns: TRANSLATION_NAMESPACES.GLOBAL }),
            description: errorPhoneEmpty === catchError(e) ? t('display.toast.userAccessIncomplete', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST }) : `${catchError(e)}`,
            variant: 'failed',
        });
    } finally {
        hideProgress();
    }
};

export const postEmployee = async (contextData, onSuccessfully, onFailed, payload) => {
    const {
        showProgress, hideProgress, addToast,
    } = contextData;
    const { integrateUserAccess, ...rest } = payload;

    try {
        showProgress();
        const { status, msg, data } = await createEmployeeV2({ ...rest, access_majoo_teams: integrateUserAccess ? 1 : 0 });
        // if (!status) {
        //     if (msg === 'Username already taken. Please pick another username') {
        //         throw Error(errorMessage.NAMA());
        //     } else if (msg === 'Nomor HP sudah digunakan di cabang ini, silahkan gunakan nomor hp yang lain.') {
        //         throw Error(errorMessage.TELEPON());
        //     } else if (msg === 'Email yang kamu gunakan telah terdaftar sebelumnya, gunakan email yang lain atau cek daftar karyawan') {
        //         throw Error(errorMessage.EMAIL());
        //     } else if (msg === 'NIP already used') {
        //         throw Error(errorMessage.NIP());
        //     } else {
        //         throw Error(msg);
        //     }
        // }

        /*
        TODO: remove this jika sudah aman, due to API access sudah terinclude di API create karyawan (based on card #86eq10k91)
        if (integrateUserAccess) {
            const merchantId = config.getLocalConfigByKey('parentId');
            // need enhance when multi outlet is applied
            const { cabang_name: outletName } = contextData.branchData.list.find(branch => branch.id_cabang === `${rest.outlets[0].id_outlet}`);

            await createUserAccess({
                context: contextData,
                payload: {
                    merchant_id: +merchantId,
                    detail: [{
                        user_name: rest.employee_name,
                        phone_number: rest.employee_notlp,
                        outlet_id: +rest.outlets[0].id_outlet,
                        outlet_name: outletName,
                        is_active: rest.status === 1,
                        user_id: data.id_employee,
                    }],
                },
            }, message => addToast({
                title: t('toast.success', { ns: TRANSLATION_NAMESPACES.GLOBAL }),
                description: message,
                variant: 'success',
            }));
        }
        */

        onSuccessfully(data);
    } catch (e) {
        addToast({
            title: t('toast.error', { ns: TRANSLATION_NAMESPACES.GLOBAL }),
            description: catchError(e),
            variant: 'failed',
        });
        onFailed(catchError(e));
    } finally {
        hideProgress();
    }
};

export const editEmployee = async (contextData, onSuccessfully, onFailed, payload) => {
    const {
        showProgress, hideProgress, addToast,
    } = contextData;

    try {
        showProgress();
        const res = await updateEmployeeV2(payload);

        // if (!res.status) {
        //     if (res.msg === 'Username already taken. Please pick another username') {
        //         throw Error(errorMessage.NAMA());
        //     } else if (res.msg === 'Nomor HP sudah digunakan di cabang ini, silahkan gunakan nomor hp yang lain.') {
        //         throw Error(errorMessage.TELEPON());
        //     } else if (res.msg === 'Email yang kamu gunakan telah terdaftar sebelumnya, gunakan email yang lain atau cek daftar karyawan') {
        //         throw Error(errorMessage.EMAIL());
        //     } else if (res.msg === 'NIP already used') {
        //         throw Error(errorMessage.NIP());
        //     } else {
        //         throw Error(res.msg);
        //     }
        // }
        onSuccessfully(res.data);
    } catch (e) {
        addToast({
            title: t('toast.error', { ns: TRANSLATION_NAMESPACES.GLOBAL }),
            description: catchError(e),
            variant: 'failed',
        });
        onFailed(catchError(e));
    } finally {
        hideProgress();
    }
};

export const uploadImage = async (contextData, onSuccessfully, file) => {
    const { addToast } = contextData;

    try {
        const formdata = new FormData();
        const mimeType = file.type.split('/');
        formdata.append('userfile', file, `image.${mimeType[1]}`);
        const response = await uploadImageAPI(formdata);
        if (!response.item_image_path) throw Error(response.msg);
        onSuccessfully(response.item_image_path);
    } catch (e) {
        addToast({
            title: t('toast.error', { ns: TRANSLATION_NAMESPACES.GLOBAL }),
            description: catchError(e),
            variant: 'failed',
        });
    }
};

export const resendVerification = async (contextData, onSuccessfully, payload) => {
    const {
        showProgress, hideProgress, addToast,
    } = contextData;

    const param = {
        email: payload.email,
        id_employee: payload.idEmployeeMayang,
        id_outlet: payload.outlet,
    };

    try {
        showProgress();
        const { status } = await resendVerificationCode(param);
        if (!status) throw Error();
        onSuccessfully();
    } catch (e) {
        addToast({
            title: t('toast.error', { ns: TRANSLATION_NAMESPACES.GLOBAL }),
            description: t('display.toast.resendVerificationCodeError', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST }),
            variant: 'failed',
        });
    } finally {
        hideProgress();
    }
};

export const verification = async (contextData, onSuccessfully, payload) => {
    const {
        showProgress, hideProgress, addToast,
    } = contextData;

    const param = {
        email: payload.email,
        kode_verifikasi: payload.kode,
    };

    try {
        showProgress();
        const { status, msg } = await verifikasiSupervisor(param);
        if (!status) throw Error(msg);
        onSuccessfully();
    } catch (e) {
        let message = catchError(e);
        if (message === 'Verified code failed!!') {
            message = t('display.toast.verificationError', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST });
        }
        addToast({
            title: t('toast.error', { ns: TRANSLATION_NAMESPACES.GLOBAL }),
            description: message,
            variant: 'failed',
        });
    } finally {
        hideProgress();
    }
};

export const editPIN = async (contextData, onSuccessfully, payload) => {
    const {
        showProgress, hideProgress, addToast,
    } = contextData;

    const param = {
        pin: payload.pin,
        id_employee: payload.idEmployeeMayang,
        id_outlet: payload.outlet,
    };

    try {
        showProgress();
        const { status } = await changePinEmployee(param);
        if (!status) throw Error();
        onSuccessfully();
    } catch (e) {
        addToast({
            title: t('toast.error', { ns: TRANSLATION_NAMESPACES.GLOBAL }),
            description: t('display.toast.changePINerror', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST }),
            variant: 'failed',
        });
    } finally {
        hideProgress();
    }
};

export const fetchProvinsi = async (contextData, onSuccessfully) => {
    const {
        showProgress, hideProgress, addToast,
    } = contextData;

    try {
        // showProgress();
        const { data } = await getallNegaraAndWilayah();
        const { provinsi } = data.find(x => x.id === '107');
        onSuccessfully({ provinsi });
    } catch (e) {
        addToast({
            title: t('toast.error', { ns: TRANSLATION_NAMESPACES.GLOBAL }),
            description: catchError(e),
            variant: 'failed',
        });
    } finally {
        // hideProgress();
    }
};

export const fetchBank = async (contextData, onSuccessfully) => {
    const {
        showProgress, hideProgress, addToast,
    } = contextData;

    try {
        // showProgress();
        const { data, status } = await getBank();
        if (status) onSuccessfully(data);
    } catch (e) {
        addToast({
            title: t('toast.error', { ns: TRANSLATION_NAMESPACES.GLOBAL }),
            description: catchError(e),
            variant: 'failed',
        });
    } finally {
        // hideProgress();
    }
};

export const fetchPractitionerByNik = async (contextData, nik, onSuccessfully) => {
    const {
        showProgress, hideProgress,
    } = contextData;
    showProgress();
    try {
        const res = await getPractitionerList({
            page: 1,
            limit: 1,
            search: nik
        });
        const resData = res.data;
        onSuccessfully(resData || []);
    } catch (e) {
        onSuccessfully([]);
    } finally {
        hideProgress();
    }
};

export const fetchEmployeeHistory = async (contextData, id, onSuccessfully) => {
    const {
        showProgress, hideProgress, addToast, detailForm: { detail = {} }
    } = contextData;
    showProgress();
    try {
        const res = await getEmployeeHistory(id);
        const resData = res.data;

        const { data: dataEmployeeList = [] } = await getEmployeeTypeList();
        const getEmployeeType = (empType) => {
            const findEmpType = dataEmployeeList.find(x => String(x.id) === String(empType));
            if (findEmpType) return findEmpType.name;
            return '';
        };

        onSuccessfully((resData || []).map((x, index) => ({ ...x, index, isTerminated: detail.isTerminated, typeName: getEmployeeType(x.type) })));
    } catch (e) {
        addToast({
            title: t('toast.error', { ns: TRANSLATION_NAMESPACES.GLOBAL }),
            description: catchError(e),
            variant: 'failed',
        });
    } finally {
        hideProgress();
    }
};

export const patchJobHistory = async (contextData, payload, onSuccessfully) => {
    const {
        showProgress, hideProgress, addToast,
    } = contextData;
    showProgress();
    try {
        await updateEmployeeHistory(payload);
        onSuccessfully();
    } catch (e) {
        addToast({
            title: t('toast.error', { ns: TRANSLATION_NAMESPACES.GLOBAL }),
            description: catchError(e),
            variant: 'failed',
        });
    } finally {
        hideProgress();
    }
};

export const fetchEmployeeSubs = async (contextData, id, onSuccessfully) => {
    const {
        showProgress, hideProgress, addToast,
    } = contextData;
    showProgress();
    try {
        const res = await getEmployeeSubs(id);
        onSuccessfully(res.data || []);
    } catch (e) {
        addToast({
            title: t('toast.error', { ns: TRANSLATION_NAMESPACES.GLOBAL }),
            description: catchError(e),
            variant: 'failed',
        });
    } finally {
        hideProgress();
    }
};

export const moveEmployee = async (contextData, data, onSuccessfully, employeeTypeOptions) => {
    const {
        showProgress, hideProgress, addToast,
    } = contextData;
    showProgress();
    try {
        const selectedEmployeeType = employeeTypeOptions.find(x => String(x.value) === String(data.employeeType.to));

        const payload = {
            id: data.id,
            department_id: data.department.to,
            position_id: data.position.to,
            start_date: data.effectiveDate.to,
            ...(data.endDate.to && selectedEmployeeType && selectedEmployeeType.hasEndDate) && {
                end_date: data.endDate.to,
            },
            outlet_id: Number(data.outlet.to),
            report_to: data.directReport.to,
            type: data.employeeType.to,
            reason: data.reason,
            subs: data.members.map(({ id, directReport }) => ({
                sub_id: id,
                report_to: directReport,
            }))
        };
        if (data.additionalFile && data.additionalFile.length > 0) {
            await uploadImage(contextData, (filePath) => {
                Object.assign(payload, { additional_attachment_url: filePath });
            }, data.additionalFile[0]);
        }
        const res = await transferEmployee(payload);
        onSuccessfully(res.data);
    } catch (e) {
        addToast({
            title: t('toast.error', { ns: TRANSLATION_NAMESPACES.GLOBAL }),
            description: catchError(e),
            variant: 'failed',
        });
    } finally {
        hideProgress();
    }
};

export const terminateEmployee = async (contextData, onSuccessfully, payload) => {
    const {
        showProgress, hideProgress, addToast,
    } = contextData;

    try {
        showProgress();
        await terminateEmployeeV2(payload);

        onSuccessfully();
    } catch (e) {
        addToast({
            title: t('toast.error', { ns: TRANSLATION_NAMESPACES.GLOBAL }),
            description: catchError(e),
            variant: 'failed',
        });
    } finally {
        hideProgress();
    }
};

export const deleteEmployeeV2 = async (contextData, onSuccessfully, payload) => {
    const {
        showProgress, hideProgress, addToast,
    } = contextData;

    try {
        showProgress();
        await apiDeleteEmployeeV2(payload);

        onSuccessfully();
    } catch (e) {
        addToast({
            title: t('toast.error', { ns: TRANSLATION_NAMESPACES.GLOBAL }),
            description: catchError(e),
            variant: 'failed',
        });
    } finally {
        hideProgress();
    }
};

export const downloadImportTemplate = async (contextData) => {
    const {
        showProgress, hideProgress, addToast, lang, t: trans
    } = contextData;
    try {
        const alertExport = new AlertDialogFunction({
            title: trans('modal.import.title', { context: 'export' }),
            description: trans('modal.import.description', { context: 'exportProcess' }),
            dialogType: 'primary',
            labelConfirm: trans('label.confirm', { ns: 'translation' }),
            singleButton: true,
        });
        showProgress();
        await exportEmployeeTemplate({ lang });
        alertExport.show()
    } catch (error) {
        addToast({
            title: t('translation:toast.error', 'Gagal!'),
            description: catchError(error),
        });
    } finally {
        hideProgress();
    }
};

export const handleImportEmployee = async (contextData, data, onSuccessfully) => {
    const { lang, showProgress, hideProgress, addToast, checkRunningImport, setIsImportRunning } = contextData;
    const { file } = data;
    const payload = new FormData();
    payload.append('file', file[0]);
    payload.append('lang', lang);
    try {
        showProgress();
        await importEmployeeData(payload);
        if (onSuccessfully) onSuccessfully();
        setIsImportRunning(prev => ({ ...prev, employee: true }));
        setTimeout(() => checkRunningImport(), 1000);
    } catch (error) {
        addToast({
            title: t('translation:toast.error', 'Gagal!'),
            description: catchError(error),
            variant: 'failed',
        });
    } finally {
        hideProgress();
    }
};

export const handleImportEmployeeDoc = async (contextData, data, onSuccessfully) => {
    const { showProgress, hideProgress, addToast, checkRunningImport, getContentTranslation, setIsImportRunning  } = contextData;
    const { file, docType } = data;
    const payload = new FormData();
    payload.append('file', file[0]);
    payload.append('document_type', docType);
    try {
        showProgress();
        await importEmployeeDocument(payload);
        if (onSuccessfully) onSuccessfully();
        setIsImportRunning(prev => ({ ...prev, document: true }));
        setTimeout(() => checkRunningImport(), 1000);
    } catch (error) {
        let errorMessage = catchError(error);
        if (error.cause && error.cause.error && Object.values(error.cause.error).length) {
            errorMessage = getContentTranslation(catchError(Object.values(error.cause.error)[0]));
        }
        
        addToast({
            title: t('translation:toast.error', 'Gagal!'),
            description: errorMessage,
            variant: 'failed',
        });
    } finally {
        hideProgress();
    }
};

export const handleExportData = async (contextData) => {
    const {
        filterBranch, searchKeyword, filterStatus, showProgress, hideProgress, addToast, setShowExportBanner,
    } = contextData;
    try {
        const payload = {
            ...(searchKeyword !== '') && { search: searchKeyword },
            ...(filterStatus !== '') && { is_terminated: Number(filterStatus) },
            ...(!!filterBranch) && { outlet_id: Number(filterBranch) },
        }
        showProgress();
        await exportEmployee(payload);
        setShowExportBanner(true)
    } catch (error) {
        addToast({
            title: t('translation:toast.error', 'Gagal!'),
            description: catchError(error),
            variant: 'failed',
        });
    } finally {
        hideProgress();
    }
};

export const confirmExport = async (contextData) => {
    const {
        addToast, isMobile,
    } = contextData;
    try {
        const res = await checkExportEmployee();
        const isProcessing = !res.data.status;
        const dialogEksport = new AlertDialogFunction({
            isMobile,
            title: t('modal.export.title', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST }),
            description: t('modal.export.description', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST }),
            dialogType: 'primary',
            singleButton: true,
            labelConfirm: t('label.confirm', { ns: TRANSLATION_NAMESPACES.GLOBAL }),
            onConfirm: () => handleExportData(contextData),
        });

        const dialog = new AlertDialogFunction({
            isMobile,
            title: t('modal.export.title', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST }),
            description: t('modal.export.description', { ns: TRANSLATION_NAMESPACES.EMPLOYEELIST, context: isProcessing ? 'process' : 'confirm' }),
            dialogType: 'primary',
            ...isProcessing ? {
                singleButton: true,
                actionButtonProps: {
                    buttonType: 'ghost'
                },
                labelConfirm: t('label.close', { ns: TRANSLATION_NAMESPACES.GLOBAL }),
            } : {
                labelConfirm: t('label.continue', { ns: TRANSLATION_NAMESPACES.GLOBAL }),
                labelCancel: t('label.cancel', { ns: TRANSLATION_NAMESPACES.GLOBAL }),
                onConfirm: () => setTimeout(() => dialogEksport.show(), 100),
            }
        });

        dialog.show();
    } catch (error) {
        addToast({
            title: t('translation:toast.error', 'Gagal!'),
            description: catchError(error),
            variant: 'failed',
        });
    }
};

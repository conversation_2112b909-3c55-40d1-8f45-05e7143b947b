import React, { useRef, useState } from 'react';
import {
    Paper,
    Separator,
    Flex,
    Box,
    Banner, BannerDescription, BannerClose,
} from '@majoo-ui/react';
import { CircleInfoFilled } from '@majoo-ui/icons';
import { Trans, useTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import ExportBanner from '~/components/retina/export/ExportBanner';
import CoreHOC from '../../../../core/CoreHOC';
import TitleSection from './components/TitleSection';
import FilterSection from './components/FilterSection';
import EmployeeContextProvider from './context/EmployeeContext';
import List from './components/List';
import FormModal from './components/FormModal';
import CloseInteraction from './components/CloseInteraction';
import { modeEnum } from './settings/constants';
import ConfirmationModal from './components/ConfirmationModal';
import { BannerText } from '../../../../components/retina';
import FormOverview from './components/FormOverview';
import { useMediaQuery } from '../../../../utils/useMediaQuery';
import MoveEmployeeModal from './components/MoveEmployeeModal';
import TerminateEmployeeModal from './components/TerminateEmployeeModal';
import ModalHistory from './components/ModalHistory';
import ModalAdditionalFile from './components/ModalAdditionalFile';
import ModalChangeHistory from './components/ModalChangeHistory';
import ImportDialog from './components/ImportDataDialog';
import ImportBanner from './components/ImportBanner';
import ImagePreviewModal from './components/ImagePreview';

const Employee = (props) => {
    const { t } = useTranslation('Karyawan/PengaturanKaryawan/employeeList');
    const isMobile = useMediaQuery('(max-width: 767px)');
    const [openFormModal, setOpenFormModal] = useState(false);
    const [openOverviewModal, setOpenOverviewModal] = useState(false);
    const [openCloseInteraction, setOpenCloseInteraction] = useState(false);
    const [openMoveEmployeeModal, setOpenMoveEmployeeModal] = useState(false);
    const [openTerminateEmployeeModal, setOpenTerminateEmployeeModal] = useState(false);
    const [openMoveSubordinateModal, setOpenMoveSubordinateModal] = useState(false);
    const [previewImage, setPreviewImage] = useState('');
    const [titlePreviewImage, setTitlePreviewImage] = useState('');
    const [showBanner, setShowBanner] = useState(true);
    const [showExportBanner, setShowExportBanner] = useState(false);

    const [callback, setCallback] = useState(null);
    const [modeForm, setModeForm] = useState('');
    const [detailForm, setDetailForm] = useState({});
    const [deleteForm, setDeleteForm] = useState({});
    const tableRef = useRef();
    const modalAdditionalFileRef = useRef();
    const modalChangeHistoryRef = useRef();
    const modalHistoryRef = useRef();
    const imagePreviewDetailRef = useRef();
    const importDialogRef = useRef();
    const formId = props.router.location.hash;

    const handleClickAdd = () => {
        setModeForm(modeEnum.ADD);
        setDetailForm({});
        setOpenFormModal(true);
    };

    const handlePhotoPreview = (file) => {
        imagePreviewDetailRef.current.click();
        setPreviewImage(file.url);
        setTitlePreviewImage(file.name);
    };

    return (
        <EmployeeContextProvider
            parentProps={{
                ...props,
                isMobile,
                detailForm,
                openMoveEmployeeModal,
                modalHistoryRef,
                modalAdditionalFileRef,
                modalChangeHistoryRef,
                tableRef,
                openTerminateEmployeeModal,     
                openMoveSubordinateModal,
                showExportBanner,
                importDialogRef,
                setOpenMoveSubordinateModal,
                setDetailForm,
                setOpenMoveEmployeeModal,
                setOpenTerminateEmployeeModal,
                handlePhotoPreview,
                setShowExportBanner,
            }}>
            <React.Fragment>
                {showExportBanner && (
                    <Box css={{ marginBottom: '$spacing-03'}}>        
                        <ExportBanner
                            showExportBanner={showExportBanner}
                            onDismiss={() => {
                                setShowExportBanner(false);
                            }}
                        />
                    </Box>
                )}
                <div id="a-dialog" />
                {showBanner && (
                    <Banner variant="info" bannerBlock onRemove={() => setShowBanner(false)} css={{ mb: '$cozy' }}>
                        <Flex justify="between" css={{ width: '$full' }}>
                            <Flex direction="row" gap={3} align="start" wrap="wrap" css={{ width: '$full' }}>
                                <CircleInfoFilled />
                                <BannerDescription>
                                    <Trans t={t} i18nKey="bannerMenu" />
                                </BannerDescription>
                            </Flex>
                            <BannerClose />
                        </Flex>
                    </Banner>
                )}
                <Paper
                    css={{
                        padding: 0,
                        backgroundColor: 'transparent',
                        boxShadow: 'none',
                        '@md': {
                            padding: '$spacing-05',
                            backgroundColor: '$white',
                            boxShadow: '0px 2px 12px #00000014',
                            marginBottom: '28px',
                        },
                    }}
                >
                    <Flex
                        direction="column"
                        gap={5}
                    >
                        <TitleSection onClickAdd={handleClickAdd} />
                        <BannerText css={{ my: 0 }} />
                        <ImportBanner />
                        <Separator css={{ display: 'none', '@md': { display: 'block' } }} />
                        <FilterSection
                            onClickAdd={handleClickAdd}
                            handleFetchTable={() => tableRef.current.handleFetchTable()}
                        />
                        <Separator css={{ display: 'none', '@md': { display: 'block' } }} />
                        <List
                            ref={tableRef}
                            setOpenFormModal={setOpenFormModal}
                            setModeForm={setModeForm}
                            setDetailForm={setDetailForm}
                            setDeleteForm={setDeleteForm}
                            setOpenOverviewModal={setOpenOverviewModal}
                            isMobile={isMobile}
                        />
                    </Flex>
                </Paper>
                {openFormModal ? (
                    <FormModal
                        open={openFormModal}
                        onOpenChange={() => setOpenCloseInteraction(true)}
                        mode={modeForm}
                        setForm={setOpenFormModal}
                        detailForm={detailForm}
                        formId={formId}
                        setMode={setModeForm}
                        router={props.router}
                        setCallback={setCallback}
                        handleFetchTable={(paramQuery) => tableRef.current.handleFetchTable(paramQuery)}
                        queryTable={tableRef.current.queryTable}
                        setOpenOverviewModal={setOpenOverviewModal}
                    />
                ) : null}
                {openOverviewModal && (
                    <FormOverview
                        isOpen={openOverviewModal}
                        setIsOpen={setOpenOverviewModal}
                        mode={modeForm}
                        isMobile={isMobile}
                    />
                )}
                {openMoveEmployeeModal && <MoveEmployeeModal />}
                {openTerminateEmployeeModal && (
                    <TerminateEmployeeModal
                        data={deleteForm}
                        handleFetchTable={() => tableRef.current.handleFetchTable()}
                        isMobile={isMobile}
                    />
                )}
                <ModalHistory ref={modalHistoryRef} />
                <ModalAdditionalFile ref={modalAdditionalFileRef} />
                <ModalChangeHistory ref={modalChangeHistoryRef} />
                <CloseInteraction
                    setOpenCloseInteraction={setOpenCloseInteraction}
                    setOpenFormModal={setOpenFormModal}
                    openCloseInteraction={openCloseInteraction}
                    callback={callback}
                    mode={modeForm}
                />
                <ConfirmationModal />
                <ImagePreviewModal ref={imagePreviewDetailRef} title={`File: ${titlePreviewImage}`} image={previewImage} />
                <ImportDialog ref={importDialogRef} />
            </React.Fragment>
        </EmployeeContextProvider>
    );
};

const mapStateToProps = (state) => ({
    branchData: state.branch,
});

export default connect(mapStateToProps, null)(CoreHOC(Employee));

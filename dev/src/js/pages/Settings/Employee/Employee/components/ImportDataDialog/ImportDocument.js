import React, { useContext } from "react";

import { Heading, Paper, Flex, Separator, FormLabel, Box, Text, FormHelper, Upload, InputSelect } from "@majoo-ui/react";
import { Controller, useFormContext } from "react-hook-form";
import { EmployeeContext } from "../../context/EmployeeContext";
import { IMPORT_TYPE, docTypeOptions } from "../../settings/constants";
import { LabelWrapper, ContentWrapper } from "../../settings/utils";

const ImportDocument = () => {
    const { t } = useContext(EmployeeContext); 
    const { formState: { errors }, control } = useFormContext();

    const notices = t('modal.import.notices', { returnObjects: true, context: IMPORT_TYPE.DOCUMENT, defaultValue: [] });

    return (
        <Paper responsive>
            <Flex direction="column" gap={5}>
                <Heading heading="pageTitle">
                    {t('modal.import.title', { context: IMPORT_TYPE.DOCUMENT })}
                </Heading>
                <Separator desktopOnly />
                <FormHelper css={{ fontSize: '$section-sub-title' }}>
                    {t('modal.import.helper')}
                </FormHelper>
                <Flex gap={7} justify="between" css={{ color: '$textPrimary' }}>
                    <LabelWrapper>
                        <FormLabel variant="required">
                            {t('modal.import.field.docType')}
                        </FormLabel>
                    </LabelWrapper>
                    <ContentWrapper>
                        <Controller
                            control={control}
                            name="docType"
                            render={({ field: { value, onChange } }) => (
                                <InputSelect
                                    option={docTypeOptions(t)}
                                    value={docTypeOptions(t).find(x => x.value === value)}
                                    onChange={val => onChange(val.value)}
                                    isInvalid={!!errors.type}
                                />
                            )}
                        />
                        {errors.docType && (
                            <FormHelper error>{errors.docType.message}</FormHelper>
                        )}
                    </ContentWrapper>
                </Flex>
                <Separator desktopOnly />
                <Flex gap={7} justify="between" css={{ color: '$textPrimary' }}>
                    <LabelWrapper>
                        <FormLabel variant="required">
                            {t('modal.import.field.upload')}
                        </FormLabel>
                    </LabelWrapper>
                    <ContentWrapper>
                        <Controller
                            control={control}
                            name="file"
                            render={({ field: { onChange, value, ...restField } }) => (
                                <Upload
                                    {...restField}
                                    fileList={value}
                                    onChange={({ file }) => onChange([file])}
                                    onRemove={() => onChange([])}
                                    multiple={false}
                                    listType="file"
                                    accept=".zip"
                                    width={150}
                                    height={150}
                                    max={1}
                                    css={{ 
                                        '& > div': {
                                            ...errors.file  && { borderColor: '$formError' }
                                        },
                                    }}
                                    showCropper
                                />
                            )}
                        />
                        {errors.file && (
                            <FormHelper error>{errors.file.message}</FormHelper>
                        )}
                    </ContentWrapper>
                </Flex>
                <Flex direction="column" gap={4}>
                    <LabelWrapper>
                        <FormLabel>
                            {t('translation:label.attention')}
                            :
                        </FormLabel>
                    </LabelWrapper>
                    <Flex direction="column" gap={3} css={{ backgroundColor: '$gray50', padding: 16, borderRadius: 4 }}>
                        {notices.map(item => (
                            <Flex gap={3}>
                                <Box
                                    css={{
                                        display: 'inline',
                                        width: 5,
                                        height: 5,
                                        backgroundColor: '$primary500',
                                        borderRadius: '100%',
                                        padding: 4,
                                        marginTop: 7,
                                    }}
                                />
                                <Text as="p" color="secondary" variant="link">
                                    {item}
                                </Text>
                            </Flex>
                        ))}
                    </Flex>
                </Flex>
            </Flex>
        </Paper>
    );
};

export default ImportDocument;


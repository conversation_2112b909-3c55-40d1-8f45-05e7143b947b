import React, { useContext } from 'react';
import {
    Box,
    Heading,
    Banner,
} from '@majoo-ui/react';
import { CircleExclamationFilled } from '@majoo-ui/icons';
import { EmployeeContext } from '../context/EmployeeContext';

const ImportBanner = () => {
    const { t, isImportRunning} = useContext(EmployeeContext);

    if (isImportRunning && !isImportRunning.employee && !isImportRunning.document) return null;

    return (
        <Banner variant="warning" bannerBlock>
            <Box style={{ marginTop: '-14px', fontSize: 'xx-small' }}>
                <CircleExclamationFilled size={18} color="#272A2A" />
            </Box>
            <Box>
                <Heading heading="sectionTitle" css={{ color: '#272A2A', fontWeight: 600, width: '-webkit-fill-available' }}>
                    {t('banner.import.title', 'Impor Data Karyawan Sedang Diproses')}
                </Heading>
                <Box style={{ paddingTop: '3px' }}>
                    {t('banner.import.description', 'Tombol "Impor data" akan dinonaktifkan sementara hingga proses Impor data karyawan Selesai')}
                </Box>
            </Box>
        </Banner>
    );
};

export default ImportBanner;

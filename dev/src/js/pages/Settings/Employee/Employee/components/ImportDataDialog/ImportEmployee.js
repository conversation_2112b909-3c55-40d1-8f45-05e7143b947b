import React, { useContext, useState } from "react";
import {
    Paper,
    StepWizard,
    Flex,
    Heading,
    Separator,
    FormHelper,
    FormLabel,
    InputRadioGroup,
    InputRadio,
    Box,
    Button,
    Text,
    AlertDialogFunction,
    Upload,
} from "@majoo-ui/react";
import { DownloadOutline } from "@majoo-ui/icons";
import { useFormContext, Controller } from "react-hook-form";
import { EmployeeContext } from "../../context/EmployeeContext";
import { stepImport, templateOptions } from "../../settings/constants";
import { LabelWrapper, ContentWrapper, importFileValidation } from "../../settings/utils";
import { downloadImportTemplate } from "../../utils/api";

const ImportEmployee = () => {
    const contextData = useContext(EmployeeContext);
    const { t } = contextData;
    const {
        control,
        setError,
        clearErrors,
        setValue,
        formState: { errors },
        setSw,
        completedSteps
    } = useFormContext();
    const [uploadCounter, setUploadCounter] = useState(null);
    const notices = t('modal.import.notices', { returnObjects: true, defaultValue: [] });

    const handleDownloadTemplate = () => {
        const alert = new AlertDialogFunction({
            title: t('modal.import.title', { context: 'export' }),
            description: t('modal.import.description', { context: 'export' }),
            dialogType: 'primary',
            labelConfirm: t('label.continue', { ns: 'translation' }),
            labelCancel: t('label.cancel', { ns: 'translation' }),
            onConfirm: () => downloadImportTemplate(contextData),
        })
        alert.show();
    };

    const handleChangeFileImport = async ({ file }) => {
        setUploadCounter(0);
        clearErrors('file');
        try {
            const { validationResult, errorMessage } = await importFileValidation({
                file,
                range: { header: 'A', range: 'A1:AU1' },
                t,
            });
            setUploadCounter(25);
            if (!validationResult) {
                setUploadCounter(0);
                setError('file', { type: 'custom', message: errorMessage });
                throw new Error(errorMessage);
            } else {
                setUploadCounter(100);
            }
            setValue('file', [file]);
        } catch (error) {
            setValue('file', []);
        } finally {
            setUploadCounter(0);
        }
    };
    
    const handleRemoveFileImport = () => {
        setValue('file', []);
    };

    return (
        <StepWizard
            completedSteps={completedSteps}
            initialStep={1}
            navContents={stepImport(t)}
            setSw={setSw}
            scrollTop
            isLazyMount={false}
            forbidClickNav
            cssNav={{
                top: '24px',
                '& > div > div': {
                    top: '-52px',
                },
            }}
        >
            <Paper responsive>
                <Flex direction="column" gap={5}>
                    <Heading heading="pageTitle">
                        {t('translation:label.exportTemplate')}
                    </Heading>
                    <Separator desktopOnly />
                    <Flex gap={7} justify="between" css={{ color: '$textPrimary' }}>
                        <LabelWrapper>
                            <FormLabel variant="required">
                                Template
                            </FormLabel>
                            <FormHelper>
                                {t('modal.import.helper', { context:  'template' })}
                            </FormHelper>
                        </LabelWrapper>
                        <ContentWrapper>
                            <Controller
                                name="template"
                                control={control}
                                render={({ field: { onChange, value } }) => (
                                    <Flex css={{ flex: '1' }}>
                                        <InputRadioGroup
                                            direction="column"
                                            gap={5}
                                            outlined
                                            onValueChange={val => onChange(val)}
                                            css={{ flex: '1' }}
                                            value={value}
                                        >
                                            {templateOptions(t).map(({ id, label, description }) => (
                                                <InputRadio
                                                    key={id}
                                                    id={id}
                                                    direction="row"
                                                    inputType="default"
                                                    outlined
                                                    css={{ flex: '1', gap: 8 }}
                                                    label={label}
                                                    value={id}
                                                    description={(
                                                        <Flex direction="column" css={{ gap: 10, mt: 0 }}>
                                                            <Box>{description}</Box>
                                                            {id === '1' && (
                                                                <Flex css={{ flex: '1' }} align="center" justify="center">
                                                                    <Button
                                                                        type="button"
                                                                        leftIcon={<DownloadOutline size={24} color="currentColor" />}
                                                                        color="primary"
                                                                        size="sm"
                                                                        css={{ flex: '1' }}
                                                                        onClick={() => handleDownloadTemplate()}
                                                                        disabled={value === '2'}
                                                                    >
                                                                        {t('importExport.step.one', 'Ekspor Template')}
                                                                    </Button>
                                                                </Flex>
                                                            )}
                                                        </Flex>
                                                    )}
                                                />
                                            ))}
                                        </InputRadioGroup>
                                    </Flex>
                                )}
                            />
                        </ContentWrapper>
                    </Flex>
                    <Separator />
                    <Flex direction="column" gap={4}>
                        <LabelWrapper>
                            <FormLabel>
                                {t('translation:label.attention')}
                                :
                            </FormLabel>
                        </LabelWrapper>
                        <Flex direction="column" gap={3} css={{ backgroundColor: '$gray50', padding: 16, borderRadius: 4 }}>
                            {notices.map(item => (
                                <Flex gap={3}>
                                    <Box
                                        css={{
                                            display: 'inline',
                                            width: 5,
                                            height: 'fit-content',
                                            backgroundColor: '$primary500',
                                            borderRadius: '100%',
                                            padding: 4,
                                            margin: 'auto 0px',
                                        }}
                                    />
                                    <Text as="p" color="secondary" variant="link" dangerouslySetInnerHTML={{ __html: item }}/>
                                </Flex>
                            ))}
                        </Flex>
                    </Flex>
                </Flex>
            </Paper>
            <Paper responsive>
                <Flex direction="column" gap={5}>
                    <Flex direction="column" gap={4} css={{ '@md': { marginBottom: '$spacing-03' } }}>
                        <Heading as="h3" heading="pageTitle">
                            {t('translation:label.importData')}
                        </Heading>
                        <Text color="secondary">
                            {t('modal.import.helper', { context:  'employee' })}
                        </Text>
                    </Flex>
                    <Separator />
                    <Flex gap={7} justify="between" css={{ color: '$textPrimary' }}>
                        <LabelWrapper>
                            <FormLabel
                                color="primary"
                                variant="required"
                                css={{
                                    '& span': {
                                        fontSize: 14,
                                        fontWeight: 600,
                                        lineHeight: '20px',
                                    },
                                }}
                            >
                                {t('importExport.uploadData', 'Unggah Data')}
                            </FormLabel>
                        </LabelWrapper>
                        <ContentWrapper>
                            <Controller
                                control={control}
                                name="file"
                                render={({ field: { value } }) => (
                                    <Upload
                                        fileList={value}
                                        onChange={val => handleChangeFileImport(val)}
                                        onRemove={handleRemoveFileImport}
                                        progress={uploadCounter || 0}
                                        listType="file"
                                        accept=".xls,.xlsx"
                                        width={150}
                                        height={150}
                                        max={1}
                                        css={{
                                            '& > div': {
                                                ...errors.file  && { borderColor: '$formError' }
                                            },
                                        }}
                                    />
                                )}
                            />
                            {errors.file && (
                                <FormHelper css={{ mt: '$spacing-03' }} error>{errors.file.message}</FormHelper>
                            )}
                        </ContentWrapper>
                    </Flex>
                </Flex>
            </Paper>
        </StepWizard>

    );
};

export default ImportEmployee;


import React, { useContext } from 'react';
import { PlusOutline } from '@majoo-ui/icons';
import {
    Button,
    Flex,
    Box,
    Heading,
    DropdownMenu,
    DropdownMenuTrigger,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
} from '@majoo-ui/react';
import PropTypes from 'prop-types';
import { EmployeeContext } from '../context/EmployeeContext';
import { FavoriteWrapper, TooltipGuidance } from '../../../../../components/retina';
import { confirmExport } from '../utils/api';
import { IMPORT_TYPE } from '../settings/constants';

const TitleSection = ({ onClickAdd }) => {
    const contextData = useContext(EmployeeContext);
    const { t, importDialogRef, isImportRunning } = contextData;

    return (
        <Flex
            justify="between"
            align="center"
            css={{ '@md': { padding: '0px $spacing-03' } }}
            wrap="wrap"
        >
            <Box>
                <FavoriteWrapper>
                    <Heading
                        as="h1"
                        heading="pageTitle"
                    >
                        {t('title', 'Daftar Karyawan')}
                    </Heading>
                    <TooltipGuidance />
                </FavoriteWrapper>
            </Box>
            <Flex
                gap={3}
                css={{
                    width: '$full',
                    padding: "$spacing-03 0px",                    
                    justifyContent: 'space-around',
                    '@lg': {
                        width: 'unset',
                        padding: 0,
                        justifyContent: 'space-between'
                    }}}
            >
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button
                            buttonType="ghost"
                            size="sm"
                            disabled={isImportRunning && (isImportRunning.employee || isImportRunning.document)}
                        >
                            {t('translation:label.importData', 'Impor Data')}
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent css={{ width: 'fit-content' }}>
                        <DropdownMenuItem onClick={() => importDialogRef.current.openModal(IMPORT_TYPE.EMPLOYEE)}>
                            {t('modal.import.title')}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => importDialogRef.current.openModal(IMPORT_TYPE.DOCUMENT)}>
                            {t('modal.import.title', { context: 'document' })}
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
                <Button
                    buttonType="ghost"
                    size="sm"
                    onClick={() => confirmExport(contextData)}
                >
                    {t('translation:label.exportData', 'Ekspor Data')}
                </Button>
                <Box css={{ display: 'none', '@md': { display: 'block' } }}>
                    <Button
                        onClick={onClickAdd}
                        size="sm"
                        leftIcon={<PlusOutline color="currentColor" />}
                        css={{ fontWeight: 600 }}
                    >
                        {t('display.label.addEmployee', 'Tambah Karyawan')}
                    </Button>
                </Box>
            </Flex>
        </Flex>
    );
};

TitleSection.propTypes = {
    onClickAdd: PropTypes.func.isRequired,
};

export default TitleSection;

import React, { useContext, useState, forwardRef, useImperativeHandle } from 'react';
import {
    PageDialog,
    Flex,
    Button,
    AlertDialogFunction,
} from '@majoo-ui/react';
import { FormProvider, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { EmployeeContext } from '../../context/EmployeeContext';
import { IMPORT_TYPE, IMPORT_TEMPLATE } from '../../settings/constants';
import ImportDocument from './ImportDocument';
import ImportEmployee from './ImportEmployee';
import { handleImportEmployee, handleImportEmployeeDoc } from '../../utils/api';

const ImportDialog = forwardRef((_, ref) => {
    const contextData = useContext(EmployeeContext);
    const [isOpen, setOpen] = useState(false);
    const [sw, setSw] = useState({});
    const [completedSteps, setCompletedSteps] = useState([false, false]);

    const {
        t,
    } = contextData;

    const schema = yup.object({
        step: yup.string(),
        type: yup.string(),
        docType: yup.string().when('type', {
            is: val => val === IMPORT_TYPE.DOCUMENT,
            then: yup.string().required(t('display.inputErrorMessage.cantEmpty', { field: t('modal.import.field.docType') })),
        }),
        file: yup.array().min(1, t('display.inputErrorMessage.cantEmpty', { field: t('modal.import.field.upload') })),
        template: yup.string(),
    });

    const useFormHook = useForm({
        resolver: yupResolver(schema),
        defaultValues: {
            step: 1,
            file: [],
            type: IMPORT_TYPE.DOCUMENT,
            template: "1",
            
        },
    });

    const { reset, watch, handleSubmit, setValue, } = useFormHook;
    
    const { step, type } = watch();

    const openModal = (importType = IMPORT_TYPE.DOCUMENT) => {
        reset({ 
            step: 1,
            file: [],
            type: importType,
            template: IMPORT_TEMPLATE.DONT_HAVE_TEMPLATE,
        });
        setOpen(true);
    };

    const closeModal = () => {
        const alert = new AlertDialogFunction({
            title: t('modal.import.title', { context: 'close' }),
            description: t('modal.import.description', { context: 'close' }),
            dialogType: 'negative',
            labelConfirm: t('label.continue', { ns: 'translation' }),
            labelCancel: t('label.cancel', { ns: 'translation' }),
            onConfirm: () => setOpen(false),
        })
        alert.show();
    };

    const handleBack = () => {
        if (step === 1) {
            closeModal();
        } else {
            setValue('step', step - 1);
            sw.previousStep();
        }
    };

    const onSave = async () => {
        if (step === 2 || type === IMPORT_TYPE.DOCUMENT) {
            const handleImport = type === IMPORT_TYPE.DOCUMENT ? handleImportEmployeeDoc : handleImportEmployee
            const alert = new AlertDialogFunction({
                title: t('modal.import.title', { context: type === IMPORT_TYPE.DOCUMENT && type }),
                description: t('modal.import.description', { name: t('modal.import.title', { context: type === IMPORT_TYPE.DOCUMENT && type }) }),
                dialogType: 'primary',
                labelConfirm: t('label.continue', { ns: 'translation' }),
                onConfirm: () => handleSubmit((data) => handleImport(contextData, data, () => setOpen(false)))(),
                singleButton: true,
            })
            alert.show()
        } else {
            setCompletedSteps((c) => {
                const arr = c.map((_c, index) => index < step);
                return arr;
            });
            setValue('step', step + 1);
            sw.nextStep();
        }
    };

    useImperativeHandle(ref, () => ({
        openModal,
        closeModal,
    }));

    return (
        <PageDialog open={isOpen} onOpenChange={closeModal}>
            <PageDialog.Title>
                {t('modal.import.title', { context: type === IMPORT_TYPE.DOCUMENT && type })}
            </PageDialog.Title>
                <PageDialog.Content wrapperSize={type === IMPORT_TYPE.DOCUMENT ? 'md' : 'lg'}>
                <FormProvider 
                    {...{
                        ...useFormHook,
                        completedSteps,
                        setSw,
                    }}
                >
                    {type === IMPORT_TYPE.DOCUMENT
                        ? <ImportDocument />
                        : <ImportEmployee /> 
                    }
                </FormProvider>
            </PageDialog.Content>
            <PageDialog.Footer wrapperSize="md">
                <Flex justify="end" gap={3} css={{ flex: 1, '@lg': { flex: 'unset' }}}>
                    <Button
                        type="button"
                        buttonType="secondary"
                        onClick={handleBack}
                        css={{
                            width: '$full',
                            '@lg': {
                                width: 'fit-content'
                            }
                        }}
                    >
                        {step === 2 ? t('translation:label.back') : t('translation:label.cancel')}
                    </Button>
                    <Button
                        onClick={onSave}
                        css={{
                            width: '$full',
                            '@lg': {
                                width: 'fit-content'
                            }
                        }}
                    >
                        {(step === 2 || type === IMPORT_TYPE.DOCUMENT) ? t('translation:label.save') : t('translation:label.nextThen')}
                    </Button>
                </Flex>
            </PageDialog.Footer>
        </PageDialog>
    );
});

export default ImportDialog;
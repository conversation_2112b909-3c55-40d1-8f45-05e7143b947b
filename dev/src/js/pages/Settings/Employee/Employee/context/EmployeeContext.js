import React, { useContext, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { ToastContext } from '@majoo-ui/react';
import { useTranslation } from 'react-i18next';
import { catchError } from '~/utils/helper';
import * as employeeApi from '~/data/employee';
import { TRANSLATION_NAMESPACES } from '../settings/constants';

const setValueContext = (parentProps) => {
    const { addToast } = useContext(ToastContext);
    const [searchKeyword, setSearchKeyword] = useState('');
    const [filterStatus, setFilterStatus] = useState('');
    const [key, setKey] = useState(0);
    const [selectedEmployees, setSelectedEmployees] = useState([]);
    const [openConfirmation, setOpenConfirmation] = useState(false);
    const [modalConfirmation, setModalConfirmation] = useState({});
    const [temporaryTableData, setTemporaryTableData] = useState([]);
    const [isImportRunning, setIsImportRunning] = useState({
        employee: false,
        document: false,
    });

    const contextData = {
        ...parentProps,
        addToast,
        searchKeyword,
        filterStatus,
        modalConfirmation,
        openConfirmation,
        selectedEmployees,
        temporaryTableData,
        key,
        isImportRunning,
    };

    const handleOpenConfirmationModal = ({
        title, description, buttons, withCancelButton = true,
    }) => {
        setOpenConfirmation(true);
        setModalConfirmation({
            title,
            description,
            buttons,
            withCancelButton,
        });
    };

    const handleCloseConfirmationModal = () => {
        setOpenConfirmation(false);
    };

    return {
        ...contextData,
        setSearchKeyword,
        setFilterStatus,
        handleOpenConfirmationModal,
        handleCloseConfirmationModal,
        setOpenConfirmation,
        setSelectedEmployees,
        setTemporaryTableData,
        setKey,
        setIsImportRunning,
    };
};

export const EmployeeContext = React.createContext();

const EmployeeContextProvider = ({ children, parentProps }) => {
    let intervalCheckRunningProcess = null;
    const value = setValueContext(parentProps);
    const { t, i18n } = useTranslation([TRANSLATION_NAMESPACES.EMPLOYEELIST, TRANSLATION_NAMESPACES.GLOBAL, TRANSLATION_NAMESPACES.EMPLOYEE_SETTING]);

    const checkRunningImport = async () => {
        const { addToast, setIsImportRunning } = value;
        try {
            const [
                importEmployeeResponse,
                importDocumentResponse,
            ] = await Promise.all([
                employeeApi.checkRunningImportEmployeeServices(),
                employeeApi.checkRunningImportDocumentServices(),
            ]);
            const { data: importEmployee  } = importEmployeeResponse;
            const { data: importDocument } = importDocumentResponse;
            const isRunning = {
                employee: !importEmployee.status ?? false,
                document: !importDocument.status ?? false,
            };
            setIsImportRunning(isRunning);
            if (isRunning.employee || isRunning.document) {
                if (intervalCheckRunningProcess) clearInterval(intervalCheckRunningProcess);
                intervalCheckRunningProcess = setInterval(() => {
                    checkRunningImport();
                }, 10000);
            } else if (intervalCheckRunningProcess) {
                clearInterval(intervalCheckRunningProcess);
            }
        } catch (error) {
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(error),
                variant: 'failed',
                position: 'top-right',
                dismissAfter: 3000,
            });
        }
    };

    useEffect(() => {
        checkRunningImport();
        if (intervalCheckRunningProcess) clearInterval(intervalCheckRunningProcess);
    }, []);

    return (
        <EmployeeContext.Provider value={{ ...value, t, lang: i18n.language, checkRunningImport }}>
            {children}
        </EmployeeContext.Provider>
    );
};

EmployeeContextProvider.propTypes = {
    children: PropTypes.element.isRequired,
    parentProps: PropTypes.shape({}),
};

EmployeeContextProvider.defaultProps = {
    parentProps: {},
};

export default EmployeeContextProvider;

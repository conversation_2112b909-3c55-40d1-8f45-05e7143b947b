import { fetchApi } from '../../services/api';

const endPoints = {
    employeev11: '0_0_11/employee/employee',
    employee: '0_0_12/employee/employee',
    employeePayrollV14: '0_0_14/employee/payroll',
    updateLaporanAbsensi: '0_0_10/laporan/absensi',
    employeeDetail: '0_0_13/employee/detail',
    newEmployeeDetail: '0_0_14/employee/payroll_detail',
    employeePin: '0_0_11/employee/pin',
    devices: '0_0_10/employee/devices',
    resendCode: '0_0_10/employee/resend_code',
    employeeSupervisor: '0_0_11/employee/supervisor',
    verifikasiSupervisor: '0_0_12/employee/activated_supervisor',
    employeeNIK: 'employee/employee_nik',
    employeeShift: 'api/v1/shift',

    // log activity
    logActivity: '/0_0_11/absensi/log',

    // absensi preset
    presenceSetting: '/0_0_11/absensi/setting_absensi',

    // V13
    employeeList: '0_0_13/employee',

    // V14
    listEmployeeV14: '0_0_14/employee',
    employeev14: '0_0_14/employee/employee',
    employeeDetailV14: '0_0_14/employee/detail',
    employeeListv14: '0_0_14/employee',
    detailEmployee: '0_0_14/employee/detail',
    employeePayrollDetailV14: '0_0_14/employee/payroll_detail',
    employeeShiftV14: '0_0_14/employee/list_shift',

    notificationDetail: 'api/v1/notification',
    employeListMajooTeam: 'api/v1/employee/access',


    // new service
    employeev1: 'api/v1/employee',
    employeeListv1: 'api/v1/employee/list',
    sendUserAccess: 'api/v1/employee',

    // radius
    presenceRadius: '0_0_11/absensi/setting/radius',

    // employee V2
    employeeV2: 'api/v2/employee',
};
// new service
export const getNewList = payload => fetchApi(endPoints.employeev1, payload, 'get', { serviceDomainType: 'user-management', authType: 'bearer' });
export const getNewEmployeeList = payload => fetchApi(endPoints.employeeListv1, payload, 'get', { serviceDomainType: 'user-management', authType: 'bearer' });
export const sendUserAccess = payload => fetchApi(endPoints.sendUserAccess, payload, 'post', { serviceDomainType: 'user-management', authType: 'bearer' });

export const createSupervisor = payload => fetchApi(endPoints.employeeSupervisor, payload, 'post');
export const getList = payload => fetchApi(endPoints.employeeList, payload);
export const getAllEmployee = payload => fetchApi(`${endPoints.employee}`, payload);
export const createEmployee = payload => fetchApi(`${endPoints.employeePayrollV14}`, payload, 'post');
export const updateEmployee = payload => fetchApi(`${endPoints.employeePayrollV14}`, payload, 'put');
export const deleteEmployee = payload => fetchApi(`${endPoints.employeev14}`, payload, 'delete');

export const getPresenceSetting = payload => fetchApi(`${endPoints.presenceSetting}`, payload);
export const updatePresenceSetting = payload => fetchApi(`${endPoints.presenceSetting}`, payload, 'put');

export const getDetail = (id, payload) => fetchApi(endPoints.employeeDetail, payload, 'get', { slashId: id });
export const getNewDetail = (id, payload) => fetchApi(endPoints.newEmployeeDetail, payload, 'get', { slashId: id });

export const getEmployeeShifts = payload => fetchApi(`${endPoints.employeeShift}`, payload, 'get', { serviceDomainType: 'payroll', authType: 'bearer' });
export const createEmployeeShift = payload => fetchApi(`${endPoints.employeeShift}`, payload, 'post', { serviceDomainType: 'payroll', authType: 'bearer' });
export const deleteWeeklyEmployeeShifts = (employeeId, payload) => fetchApi(`${endPoints.employeeShift}/employee/${employeeId}?start_date=${payload.start_date}&end_date=${payload.end_date}`, {}, 'delete', { serviceDomainType: 'payroll' });
export const deleteAllEmployeeShifts = (employeeId, payload) => fetchApi(`${endPoints.employeeShift}/employee/${employeeId}/all?start_date=${payload.start_date}&end_date=${payload.end_date}`, {}, 'delete', { serviceDomainType: 'payroll' });
export const duplicateEmployeeShifts = (employeeId, payload) => fetchApi(`${endPoints.employeeShift}/employee/${employeeId}/duplicate`, payload, 'post', { serviceDomainType: 'payroll' });

export const changePinEmployee = payload => fetchApi(endPoints.employeePin, payload, 'put');

export const updateLaporanAbsensi = payload => fetchApi(endPoints.updateLaporanAbsensi, payload, 'put');
export const fetchLogActivity = (payload) => fetchApi(endPoints.logActivity, payload, 'get');

export const getDevices = payload => fetchApi(endPoints.devices, payload);
export const updateDevice = payload => fetchApi(endPoints.devices, payload, 'put');
export const resendVerificationCode = payload => fetchApi(endPoints.resendCode, payload, 'post');
export const verifikasiSupervisor = payload => fetchApi(endPoints.verifikasiSupervisor, payload, 'post');
export const getEmployeeNIK = () => fetchApi(endPoints.employeeNIK);

export const getDetailEmployeeV14 = (id, payload) => fetchApi(endPoints.employeePayrollDetailV14, payload, 'get', { slashId: id });

export const getDetailMajooTeamsNotification = (id, payload) => fetchApi(endPoints.notificationDetail, payload, 'get', {
    slashId: id,
    serviceDomainType: 'payroll',
    authType: 'bearer',
});

export const getEmployeeListsMajooTeam = (payload) => fetchApi(endPoints.employeListMajooTeam, payload, 'get', {
    serviceDomainType: 'user-management',
    authType: 'bearer',
});

export const createEmployeeV14 = payload => fetchApi(endPoints.employeePayrollV14, payload, 'post');
export const updateEmployeeV14 = payload => fetchApi(endPoints.employeePayrollV14, payload, 'put');
export const getListEmployeeV14 = payload => fetchApi(endPoints.employeeListv14, payload);
export const getEmployeeShiftV14 = payload => fetchApi(`${endPoints.employeeShiftV14}`, payload);

export const getRadiusSetting = payload => fetchApi(`${endPoints.presenceRadius}`, payload);
export const updateRadiusSetting = payload => fetchApi(`${endPoints.presenceRadius}`, payload, 'put');

// employee V2
export const getEmployeeV2List = payload => fetchApi(endPoints.employeeV2, payload, 'get', { serviceDomainType: 'user-management', authType: 'bearer' });
export const getEmployeeV2 = slashId => fetchApi(endPoints.employeeV2, {}, 'get', {
    slashId,
    serviceDomainType: 'user-management',
    authType: 'bearer'
});
export const createEmployeeV2 = payload => fetchApi(endPoints.employeeV2, payload, 'post', {
    serviceDomainType: 'user-management',
    authType: 'bearer'
});
export const updateEmployeeV2 = ({ id, ...payload }) => fetchApi(endPoints.employeeV2, payload, 'put', {
    slashId: id,
    serviceDomainType: 'user-management',
    authType: 'bearer'
});
export const getSuperior = payload => fetchApi(endPoints.employeeV2, payload, 'get', {
    slashId: 'list-superior',
    serviceDomainType: 'user-management',
    authType: 'bearer'
});
export const getEmployeeHistory = id => fetchApi(endPoints.employeeV2, {}, 'get', {
    slashId: `${id}/job-history`,
    serviceDomainType: 'user-management',
    authType: 'bearer'
});
export const updateEmployeeHistory = ({ id, historyId, ...payload}) => fetchApi(endPoints.employeeV2, payload, 'patch', {
    slashId: `${id}/job-history/${historyId}`,
    serviceDomainType: 'user-management',
    authType: 'bearer',
    customBody: JSON.stringify(payload),
});
export const getEmployeeSubs = (id) => fetchApi(endPoints.employeeV2, {}, 'get', {
    slashId: `${id}/subs`,
    serviceDomainType: 'user-management',
    authType: 'bearer'
});
export const transferEmployee = ({id, ...payload}) => fetchApi(endPoints.employeeV2, payload, 'post', {
    slashId: `${id}/transfer`,
    serviceDomainType: 'user-management',
    authType: 'bearer'
});

export const terminateEmployeeV2 = ({ id, ...payload }) => fetchApi(endPoints.employeeV2, payload, 'patch', {
    slashId: `${id}/terminate`,
    serviceDomainType: 'user-management',
    authType: 'bearer',
    customBody: JSON.stringify(payload),
});

export const deleteEmployeeV2 = ({ id }) => fetchApi(endPoints.employeeV2, {}, 'delete', {
    slashId: `${id}`,
    serviceDomainType: 'user-management',
    authType: 'bearer',
});

// import export employee
export const exportEmployeeTemplate = payload => fetchApi(endPoints.employeeV2, payload, 'get', {
    slashId: 'import/template',
    serviceDomainType: 'user-management',
    authType: 'bearer'
});

export const importEmployeeData = payload => fetchApi(endPoints.employeeV2, payload, 'post', {
    slashId: 'import',
    serviceDomainType: 'user-management',
    authType: 'bearer',
    customBody: payload,
});

export const importEmployeeDocument = payload => fetchApi(endPoints.employeeV2, payload, 'post', {
    slashId: 'import-doc',
    serviceDomainType: 'user-management',
    authType: 'bearer',
    customBody: payload,
});

export const checkRunningImportEmployeeServices = () => fetchApi(endPoints.employeeV2, {}, 'get', {
    slashId: 'import/status',
    serviceDomainType: 'user-management',
    authType: 'bearer',
});

export const checkRunningImportDocumentServices = () => fetchApi(endPoints.employeeV2, {}, 'get', {
    slashId: 'import-doc/status',
    serviceDomainType: 'user-management',
    authType: 'bearer',
});

export const exportEmployee = payload => fetchApi(endPoints.employeeV2, payload, 'post', {
    slashId: 'export',
    serviceDomainType: 'user-management',
    authType: 'bearer',
});

export const checkExportEmployee = () => fetchApi(endPoints.employeeV2, {}, 'get', {
    slashId: 'export/status',
    serviceDomainType: 'user-management',
    authType: 'bearer',
});
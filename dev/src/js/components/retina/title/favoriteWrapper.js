import React, {
    useContext, useRef, useState,
} from 'react';
import {
    Box, ToastContext,
} from '@majoo-ui/react';
import PropTypes from 'prop-types';
import { StarFilled, StarOutline } from '@majoo-ui/icons';
import { Trans, useTranslation } from 'react-i18next';
import { get } from 'lodash';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { colors } from '../../../stitches.config';
import { ConditionalWrapper } from '../../wrapper/ConditionalWrapper';
import { createFavoriteMenu, deleteFavoriteMenu } from '../../../data/users';
import { catchError, useThrottle } from '../../../utils/helper';
import { layoutsActions } from '../../../data/layouts/layouts.reducer';

const Wrapper = ({
    children, css, className, stateProps,
}) => {
    const { addToast } = useContext(ToastContext);
    const { t, i18n: { language } } = useTranslation(['translation']);
    const {
        fetchMenu, onAssignFavorite, layouts: {
            activeMenuName, activeMenu: menuId, favorite, isMutating,
        },
    } = stateProps;
    const currentMenu = activeMenuName;
    const isFavoritePage = menuId.includes('-fav');
    const modifiedId = isFavoritePage ? menuId.replace('-fav', '') : menuId;
    const timeoutRef = useRef(null);

    const [active, setActive] = useState(
        favorite.some(s => (String(s.menu_id) === String(menuId))
            || (`${s.menu_id}-fav` === String(menuId))),
    );

    const addFavorite = async (id) => {
        try {
            const res = await createFavoriteMenu(id);
            if (res.code !== 201) throw new Error(res.message);
            addToast({
                id: 'add-favorite',
                title: t('toast.success', 'Berhasil!'),
                description: <Trans t={t} i18nKey="toast.successAddFavorite" values={{ menu: currentMenu }} defaults="Menu <strong>{{menu}}</strong> berhasil ditambahkan ke menu favorit" />,
                variant: 'success',
            });
            setActive(true);
        } catch (e) {
            const errorCode = get(e, 'cause.code');
            addToast({
                id: 'add-favorite',
                title: t('toast.error'),
                description: errorCode === 422 ? <Trans t={t} i18nKey="toast.favoriteLimit" /> : catchError(e),
                variant: 'failed',
            });
        } finally {
            fetchMenu(language);
        }
    };

    const removeFavorite = async (id) => {
        try {
            const res = await deleteFavoriteMenu(id);
            if (res.code !== 200) throw new Error(res.message);
            addToast({
                id: 'remove-favorite',
                title: t('toast.success', 'Berhasil!'),
                description: <Trans t={t} i18nKey="toast.successRemoveFavorite" values={{ menu: currentMenu }} defaults="Menu <strong>{{menu}}</strong> berhasil ditambahkan ke menu favorit" />,
                variant: 'success',
            });
            setActive(false);
        } catch (e) {
            addToast({
                id: 'remove-favorite',
                title: t('toast.error'),
                description: catchError(e),
                variant: 'failed',
            });
        } finally {
            fetchMenu(language);
        }
    };

    const throttleActive = useThrottle(
        () => {
            onAssignFavorite(true);
            clearTimeout(timeoutRef.current);
            if (active) {
                removeFavorite(modifiedId);
            } else {
                addFavorite(modifiedId);
            }
            timeoutRef.current = setTimeout(() => { onAssignFavorite(false); }, 7000);
        },
        7000,
    );

    return (
        <Box
            className={className}
            css={{
                display: 'flex', alignItems: 'center', gap: '$spacing-03', ...css,
            }}
        >
            {children}
            <Box
                as="span"
                onClick={(e) => {
                    if (isMutating) {
                        e.preventDefault();
                        return;
                    }
                    throttleActive();
                }}
                css={{ height: '$cozy', m: 'auto 0px', cursor: isMutating ? 'not-allowed' : 'pointer' }}
            >
                {active ? (
                    <StarFilled color={colors.btnAccent} />
                ) : (
                    <StarOutline color={colors.iconSecondary} />
                )}
            </Box>
        </Box>
    );
};


Wrapper.propTypes = {
    children: PropTypes.node,
    css: PropTypes.shape({}),
    className: PropTypes.string,
    stateProps: PropTypes.shape({
        fetchMenu: PropTypes.func,
        onAssignFavorite: PropTypes.func,
        layouts: PropTypes.shape({
            activeMenuName: PropTypes.string,
            activeMenu: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
            favorite: PropTypes.arrayOf(PropTypes.shape({})),
            isMutating: PropTypes.bool,
        }),
    }).isRequired,
};

Wrapper.defaultProps = {
    children: '',
    css: undefined,
    className: undefined,
};

const FavoriteWrapper = ({
    children, isNotWrapped, css, className, ...stateProps
}) => (
    <ConditionalWrapper
        condition={!isNotWrapped}
        wrapper={child => (
            <Wrapper key={`favorite-wrapper-${JSON.stringify(stateProps.favorite)}`} stateProps={stateProps} className={className} css={css}>
                {child}
            </Wrapper>
        )}
    >
        {children}
    </ConditionalWrapper>
);

FavoriteWrapper.propTypes = {
    isNotWrapped: PropTypes.bool,
    children: PropTypes.node,
    css: PropTypes.shape({}),
    className: PropTypes.string,
};

FavoriteWrapper.defaultProps = {
    children: '',
    isNotWrapped: false,
    css: undefined,
    className: undefined,
};

const mapStateToProps = state => ({
    layouts: state.layouts,
});

const mapDispatchToProps = dispatch => ({
    ...bindActionCreators({
        fetchMenu: layoutsActions.fetchMenu,
        onAssignFavorite: layoutsActions.onAssignFavorite,
    }, dispatch),
});

export default connect(mapStateToProps, mapDispatchToProps)(FavoriteWrapper);

// Mock for @majoo-ui/icons package
import React from 'react';

// Create a generic mock component for all icons
const MockIcon = (props) => React.createElement('svg', {
  'data-testid': 'mock-icon',
  ...props
});

// Create a proxy that returns MockIcon for any property access
const IconsProxy = new Proxy({}, {
  get: function(target, prop) {
    // Return MockIcon for any icon name
    return MockIcon;
  }
});

// Export specific icons that we know are used
export const CalendarOutline = MockIcon;
export const StarFilled = MockIcon;
export const StarTwoTone = MockIcon;
export const StarOutline = MockIcon;
export const ArrowLeft = MockIcon;
export const ArrowRight = MockIcon;
export const Search = MockIcon;
export const Close = MockIcon;
export const Menu = MockIcon;
export const Home = MockIcon;
export const Settings = MockIcon;
export const User = MockIcon;
export const Bell = MockIcon;
export const Mail = MockIcon;
export const Phone = MockIcon;
export const Edit = MockIcon;
export const Delete = MockIcon;
export const Add = MockIcon;
export const Remove = MockIcon;
export const Check = MockIcon;
export const Warning = MockIcon;
export const Info = MockIcon;
export const Error = MockIcon;
export const Success = MockIcon;
export const Download = MockIcon;
export const Upload = MockIcon;
export const Print = MockIcon;
export const Share = MockIcon;
export const Copy = MockIcon;
export const Paste = MockIcon;
export const Cut = MockIcon;
export const Save = MockIcon;
export const Refresh = MockIcon;
export const Sync = MockIcon;
export const Filter = MockIcon;
export const Sort = MockIcon;
export const View = MockIcon;
export const Hide = MockIcon;
export const Show = MockIcon;
export const Expand = MockIcon;
export const Collapse = MockIcon;
export const Maximize = MockIcon;
export const Minimize = MockIcon;
export const FullScreen = MockIcon;
export const ExitFullScreen = MockIcon;
export const Play = MockIcon;
export const Pause = MockIcon;
export const Stop = MockIcon;
export const Forward = MockIcon;
export const Backward = MockIcon;
export const Next = MockIcon;
export const Previous = MockIcon;
export const First = MockIcon;
export const Last = MockIcon;
export const Up = MockIcon;
export const Down = MockIcon;
export const Left = MockIcon;
export const Right = MockIcon;
export const ChevronUp = MockIcon;
export const ChevronDown = MockIcon;
export const ChevronLeft = MockIcon;
export const ChevronRight = MockIcon;
export const DoubleChevronUp = MockIcon;
export const DoubleChevronDown = MockIcon;
export const DoubleChevronLeft = MockIcon;
export const DoubleChevronRight = MockIcon;

// Default export
export default MockIcon;

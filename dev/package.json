{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "0.0.1", "description": "", "license": "ISC", "author": "", "scripts": {"build": "NODE_OPTIONS=\"--max-old-space-size=4096\" webpack --config webpack.prod.js --progress --color", "build_for_node_v17_or_higher": "NODE_OPTIONS=\"--max-old-space-size=4096 --openssl-legacy-provider\" webpack --config webpack.prod.js --progress --color", "coverage:all": "jest --coverage --collectCoverageFrom=src/js/**/*.{js,jsx}", "open:coverage": "xdg-open ./coverage/lcov-report/index.html", "open:coverage:mac": "open ./coverage/lcov-report/index.html", "prod-stats": "NODE_OPTIONS=\"--max-old-space-size=4096\" webpack --config webpack.prod.js --stats --json > stats.json", "prod-stats_for_node_v17_or_higher": "NODE_OPTIONS=\"--max-old-space-size=4096 --openssl-legacy-provider\" webpack --config webpack.prod.js --stats --json > stats.json", "start": "node --max_old_space_size=3072 node_modules/webpack/bin/webpack.js serve --config webpack.dev.js --progress --host=0.0.0.0", "start_for_node_v17_or_higher": "node --openssl-legacy-provider --max_old_space_size=4096 node_modules/webpack/bin/webpack.js serve --config webpack.dev.js --progress", "start:debug": "node --max_old_space_size=2048 node_modules/webpack/bin/webpack.js serve --config webpack.dev.js --progress --host=0.0.0.0 --env debug=true", "test": "jest", "test:coverage": "jest --coverage --silent", "clean:cache": "rm -rf node_modules/.cache/webpack && rm -rf node_modules/.cache/babel-loader"}, "jest": {"setupFilesAfterEnv": ["./src/enzyme.setup.js"], "snapshotSerializers": ["enzyme-to-json/serializer"], "moduleNameMapper": {"\\.(css|scss|less)$": "<rootDir>/__mocks__/styleMock.js", "\\.(png|jpg|gif|ttf|eot|svg|webp)$": "<rootDir>/__mocks__/fileMock.js", "^axios$": "axios/dist/node/axios.cjs", "^@majoo-ui/icons$": "<rootDir>/__mocks__/@majoo-ui/icons.js"}, "testEnvironment": "jsdom", "coveragePathIgnorePatterns": ["node_modules", ".mock.js"], "modulePathIgnorePatterns": [".*__mocks__.*"], "transformIgnorePatterns": ["/node_modules/(?!(node-fetch|fetch-blob|data-uri-to-buffer|formdata-polyfill|axios)/)"]}, "dependencies": {"@elastic/apm-rum": "^5.16.3", "@elastic/apm-rum-react": "^1.4.4", "@firebase/analytics": "^0.8.0", "@firebase/app": "0.11.1", "@firebase/auth": "1.9.0", "@firebase/remote-config": "^0.3.15", "@hookform/resolvers": "^2.9.1", "@lexical/code": "0.12.2", "@lexical/html": "0.12.2", "@lexical/link": "0.12.2", "@lexical/list": "0.12.2", "@lexical/markdown": "0.12.2", "@lexical/react": "0.12.2", "@lexical/rich-text": "0.12.2", "@lexical/selection": "0.12.2", "@lexical/table": "0.12.2", "@lexical/utils": "0.12.2", "@majoo-ui/core": "1.31.3", "@majoo-ui/helpers": "1.30.6", "@majoo-ui/icons": "1.38.39-rc.3", "@majoo-ui/react": "1.61.12", "@ramonak/react-progress-bar": "^4.1.0", "@stitches/react": "^1.2.8", "body-scroll-lock": "^3.1.5", "buffer": "^6.0.3", "core-js": "^3.34.0", "cropperjs": "^1.5.12", "crypto-browserify": "^3.12.0", "crypto-js": "^4.1.1", "draft-js": "^0.11.4", "draftjs-to-html": "^0.9.1", "elastic-apm-node": "^4.11.0", "es-abstract": "^1.20.1", "express": "^4.21.2", "html-to-draftjs": "^1.5.0", "html2canvas": "^1.4.1", "http-proxy-middleware": "^0.18.0", "i18next": "^22.0.8", "i18next-browser-languagedetector": "^7.0.1", "i18next-resources-to-backend": "^1.1.1", "immutability-helper": "^3.1.1", "json-bigint": "^1.0.0", "jwt-simple": "^0.5.6", "lexical": "0.12.2", "lodash": "^4.17.4", "moment": "^2.20.1", "pdfmake": "^0.2.20", "prop-types": "^15.7.2", "qrcode.react": "^1.0.1", "query-string": "^7.0.0", "rc-slider": "^8.7.1", "react": "^17.0.2", "react-barcode": "^1.6.1", "react-color": "^2.19.3", "react-copy-to-clipboard": "^5.0.1", "react-cropper": "^2.1.8", "react-custom-scrollbars": "^4.2.1", "react-device-detect": "^2.2.3", "react-dom": "^17.0.2", "react-dot-fragment": "^0.2.8", "react-draft-wysiwyg": "^1.14.7", "react-draggable": "^4.4.6", "react-form-with-constraints": "^0.18.0", "react-google-maps": "^9.4.5", "react-helmet": "^5.2.1", "react-highlight-words": "0.21.0", "react-hook-form": "~7.54.2", "react-html-parser": "^2.0.2", "react-i18next": "^12.0.0", "react-infinite-scroll-component": "^6.1.0", "react-joyride": "2.5.5", "react-lazy-load-image-component": "^1.5.0", "react-loadable": "^5.5.0", "react-notification-system": "^0.4.0", "react-onclickoutside": "^6.13.0", "react-portal": "^4.2.1", "react-redux": "^5.0.2", "react-remove-scroll-bar": "^2.3.8", "react-router-dom": "^5.3.1", "react-scroll": "^1.8.2", "react-select": "^1.3.0", "react-slick": "^0.30.3", "react-sortable-hoc": "^2.0.0", "react-to-print": "^3.0.5", "react-tooltip": "^4.2.21", "react-transition-group": "^2.9.0", "react-turnstile": "^1.1.4", "react-virtualized": "^9.22.6", "recharts": "^2.15.1", "recompose": "^0.30.0", "redux": "^3.6.0", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "redux-persist-seamless-immutable": "^2.0.0", "redux-promise-middleware": "^6.1.2", "redux-saga": "^1.1.3", "redux-thunk": "^2.3.0", "reduxsauce": "^1.0.0", "seamless-immutable": "^7.1.4", "stream-browserify": "^3.0.0", "uuid": "^11.1.0", "validator": "^9.1.2", "webstomp-client": "^1.2.6", "xlsx": "^0.17.5", "yup": "^0.32.11"}, "devDependencies": {"@babel/cli": "^7.26.4", "@babel/core": "^7.23.9", "@babel/plugin-proposal-decorators": "^7.25.9", "@babel/plugin-transform-class-properties": "^7.25.9", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@babel/runtime": "^7.26.10", "@svgr/webpack": "^8.1.0", "@types/json-bigint": "^1.0.1", "@types/react-slick": "^0.23.4", "@wojtekmaj/enzyme-adapter-react-17": "^0.6.6", "ajv": "^8.12.0", "babel-loader": "^9.2.1", "babel-plugin-lodash": "^3.3.4", "babel-plugin-module-resolver": "^3.2.0", "babel-plugin-transform-imports": "^2.0.0", "compression-webpack-plugin": "^10.0.0", "copy-webpack-plugin": "^13.0.0", "css-loader": "^6.9.0", "css-minimizer-webpack-plugin": "^5.0.1", "dotenv": "^8.0.0", "enzyme": "^3.11.0", "enzyme-to-json": "^3.6.2", "eslint": "^5.8.0", "eslint-config-airbnb": "^17.1.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-babel-module": "^5.1.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-jest": "^26.1.1", "eslint-plugin-jsx-a11y": "^6.1.2", "eslint-plugin-react": "^7.11.1", "express-static-gzip": "^0.3.0", "html-critical-webpack-plugin": "^2.1.0", "html-webpack-plugin": "^5.6.0", "jest": "^27.5.1", "less": "^4.1.1", "less-loader": "^11.1.4", "lodash-webpack-plugin": "^0.11.5", "mini-css-extract-plugin": "^2.7.6", "moment-locales-webpack-plugin": "^1.0.5", "node-fetch": "^2.7.0", "process": "^0.11.10", "regenerator-runtime": "^0.13.9", "speed-measure-webpack-plugin": "^1.5.0", "style-loader": "^3.3.4", "terser-webpack-plugin": "^5.3.10", "util": "^0.12.5", "vm-browserify": "^1.1.2", "webpack": "^5.98.0", "webpack-bundle-analyzer": "^4.10.1", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "webpack-merge": "^5.10.0"}}